import { useGetProfileQuery } from '@/apis/auth/queries';
import { useUserStore } from '@/store/user';
import { useCallback } from 'react';

export const useFeedbackApp = () => {
  const { data: userProfile } = useGetProfileQuery();
  const setIsShowedFeedbackAppModal = useUserStore.use.setIsShowedFeedbackAppModal();

  const onCheckSendFeedback = useCallback(() => {
    if (!userProfile?.hasFirstLikeOrComment && !userProfile?.isFeedbacked) {
      setIsShowedFeedbackAppModal(true);
      return;
    }
  }, [userProfile, setIsShowedFeedbackAppModal]);

  return {
    onCheckSendFeedback,
  };
};
