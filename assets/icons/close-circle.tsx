import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function CloseCircle({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path d='M15 10l-5 5M15 15l-5-5' stroke={color} strokeWidth={1.5} strokeLinecap='round' />
      <Path
        d='M21.5 12.25a9.25 9.25 0 11-18.5 0 9.25 9.25 0 0118.5 0z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default CloseCircle;
