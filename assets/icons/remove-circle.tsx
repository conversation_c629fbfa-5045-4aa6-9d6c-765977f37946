import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function CloseCircle({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M12 2c5.524 0 10 4.478 10 10 0 5.524-4.476 10-10 10-5.522 0-10-4.476-10-10C2 6.479 6.479 2 12 2zm0 1.5a8.5 8.5 0 100 17 8.5 8.5 0 000-17zm-4.25 7.75h8.5a.75.75 0 01.102 1.493l-.102.007h-8.5a.75.75 0 01-.102-1.493l.102-.007h8.5-8.5z'
        fill={color}
        fillRule='nonzero'
        stroke='none'
        strokeWidth={1}
      />
    </Svg>
  );
}

export default CloseCircle;
