import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function WarningOutline({ color = '#fff', size = 24, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M10.768 5c.77-1.333 2.694-1.333 3.464 0l6.928 12c.77 1.333-.192 3-1.732 3H5.572c-1.54 0-2.502-1.667-1.732-3l6.928-12z'
        stroke={color}
        strokeWidth={1.5}
      />
      <Rect x={11.75} y={7.75} width={1.5} height={7} rx={0.75} fill={color} />
      <Rect x={11.75} y={15.75} width={1.5} height={1.5} rx={0.75} fill={color} />
    </Svg>
  );
}

export default WarningOutline;
