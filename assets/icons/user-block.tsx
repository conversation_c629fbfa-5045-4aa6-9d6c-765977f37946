import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  size: number;
};

function UserBlock({ size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 184 184' fill='none' {...props}>
      <Path
        d='M120.759 80.544c17.181 31.182-19.032 47.67-42.509 47.67-23.476 0-42.508-21.343-42.508-47.67s19.032-47.67 42.508-47.67c23.477 0 42.509 21.343 42.509 47.67z'
        fill='#a6a8a7'
      />
      <Path
        d='M57.563 22.918a28.75 28.75 0 00-28.75 28.75v57.5a28.75 28.75 0 0028.75 28.75h57.499a28.754 28.754 0 0028.75-28.75v-57.5a28.746 28.746 0 00-28.75-28.75h-57.5zm28.75 51.75a17.25 17.25 0 110-34.5 17.25 17.25 0 010 34.5zm-20.126 8.82h40.251c7.141 0 12.937 4.163 12.937 11.305v2.875c0 4.6-1.967 10.189-7.36 14.524-5.336 4.278-13.616 7.038-25.725 7.038-12.133 0-20.402-2.817-25.715-7.13-5.358-4.347-7.325-9.913-7.325-14.432v-2.875c0-7.153 5.796-11.306 12.938-11.306z'
        fill='#d7d9d8'
      />
      <Path
        opacity={0.56}
        d='M41.124 137.6c.102-.476.78-.476.882 0l1.752 8.157a.45.45 0 00.347.347l8.157 1.752c.476.102.476.78 0 .882l-8.157 1.752a.45.45 0 00-.347.347l-1.752 8.157c-.102.476-.78.476-.882 0l-1.752-8.157a.451.451 0 00-.347-.347l-8.157-1.752c-.476-.102-.476-.78 0-.882l8.157-1.752a.451.451 0 00.347-.347l1.752-8.157zM146.818 32.642c-.336-.072-.336-.55 0-.623l6.096-1.309a.318.318 0 00.245-.245l1.309-6.096c.072-.336.551-.336.623 0l1.309 6.096a.32.32 0 00.245.245l6.096 1.31c.336.071.336.55 0 .622l-6.096 1.31a.32.32 0 00-.245.244l-1.309 6.096c-.072.336-.551.336-.623 0l-1.309-6.096a.318.318 0 00-.245-.245l-6.096-1.309z'
        fill='#fff'
        fillOpacity={0.45}
      />
      <Rect x={97.5664} y={86.4341} width={63.3503} height={61.6655} rx={30.8327} fill='#3D3D3D' />
      <Path
        d='M124.227 112.341l16.812 16.812M141.121 112.339l-16.812 16.813'
        stroke='#fff'
        strokeWidth={3.75428}
        strokeLinecap='round'
      />
    </Svg>
  );
}

export default UserBlock;
