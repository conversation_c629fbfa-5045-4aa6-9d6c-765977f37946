import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

const LogoHi = ({ size, color = '#0E100F', ...props }: Props) => {
  return (
    <Svg width={size} height={size} viewBox='0 0 100 100' fill='none' {...props}>
      <Rect width={size} height={size} rx={size / 2} fill='#D9FF03' fillOpacity={0.1} />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M71.285 43.895c.67 6.99-.03 13.62-1.305 18.185-.235.839-.365 1.258-.62 2.007-1.593 4.687-4.65 13.077-13.453 20.94-.076.069.084-.069 0 0-.956.855-2.069 1.712-3.21 2.26-1.929.924-3.717 1.034-3.328-.716.189-.847.755-2.002 1.416-3.35.883-1.801 1.935-3.948 2.48-6.163-11.21 2.875-22.091-1.67-24.407-10.253-2.227-8.26 4.282-17.283 14.699-20.767C32.2 41.29 27.022 35.33 34.625 32.928c7.233-2.286 15.58.174 20.082 4.227-7.117-7.884-19.601-31.706-1.38-23.26a19.465 19.465 0 013.37 2.1c7.648 6.013 13.127 16.101 14.588 27.9zm-19.147 20.85c1.728.636 3.871-.867 4.787-3.358.916-2.49.257-5.025-1.47-5.661-1.729-.637-3.872.867-4.788 3.357-.916 2.49-.257 5.026 1.471 5.662zm-5.297-3.358c-.916 2.49-3.059 3.994-4.787 3.358-1.728-.636-2.386-3.171-1.47-5.662.915-2.49 3.058-3.994 4.786-3.357 1.728.636 2.387 3.17 1.471 5.661z'
        fill='#D9FF03'
      />
      <Path d='M50 87.66h38v-38H50v38z' fill='#fff' />
    </Svg>
  );
};

export default LogoHi;
