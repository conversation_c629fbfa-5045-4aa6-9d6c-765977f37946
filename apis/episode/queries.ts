import queryKeys from '@/utils/queryKeys';
import { UseQueryOptions, useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { IEpisode } from '../podcast';
import {
  getEpisodeByIdRequest,
  getEpisodeMarkAsWatchedRequest,
  getEpisodeRateByIdRequest,
  getEpisodeRateListRequest,
  getEpisodeRateStatsRequest,
  getEpisodeWatchlistRequest,
  getEpisodesRequest,
  getWatchedRequest,
  getWatchlistRequest,
} from './requests';
import {
  Episode,
  EpisodeQueryParams,
  IEpisodeMarkAsWatchResponse,
  IEpisodeRate,
  IEpisodeWatchlistResponse,
  IGetEpisodeRateListParams,
  IGetEpisodeRateStatsResponse,
  IGetEpisodeRatesResponse,
} from './types';

export const useGetEpisodesInfiniteQuery = (query: Omit<EpisodeQueryParams, 'page'>) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest(query),
    queryFn: ({ pageParam }: { pageParam: number }) => getEpisodesRequest({ ...query, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetWatchlistQuery = (
  query: EpisodeQueryParams,
  options?: Partial<UseQueryOptions<Episode[], Error>>
) => {
  return useQuery<Episode[], Error>({
    queryKey: ['episodes', 'watchlist', query],
    queryFn: () => getWatchlistRequest(query),
    ...options,
  });
};

export const useGetWatchedQuery = (query: EpisodeQueryParams, options?: Partial<UseQueryOptions<Episode[], Error>>) => {
  return useQuery<Episode[], Error>({
    queryKey: ['episodes', 'watched', query],
    queryFn: () => getWatchedRequest(query),
    ...options,
  });
};

export const useGetEpisodeByIdQuery = (
  episodeId: string,
  options?: Partial<UseQueryOptions<IEpisode, AxiosError, IEpisode>>
) => {
  return useQuery({
    queryKey: queryKeys.episodes.getEpisodeByIdRequest(episodeId),
    queryFn: () => getEpisodeByIdRequest(episodeId),
    enabled: !!episodeId,
    ...options,
  });
};

export const useGetEpisodeRateByIdQuery = (
  episodeId: string,
  options?: Omit<UseQueryOptions<IEpisodeRate, AxiosError, IEpisodeRate>, 'queryKey'>
) => {
  return useQuery({
    queryKey: queryKeys.episodes.getEpisodeRateByIdRequest(episodeId),
    queryFn: () => getEpisodeRateByIdRequest(episodeId),
    enabled: !!episodeId,
    refetchOnMount: true,
    ...options,
  });
};

export const useGetEpisodeRateListQuery = (params: IGetEpisodeRateListParams) => {
  return useInfiniteQuery<IGetEpisodeRatesResponse, AxiosError>({
    queryKey: queryKeys.rates.episodeRateList(params.episodeId, params.limit),
    queryFn: ({ pageParam }) => getEpisodeRateListRequest({ ...params, page: pageParam as number }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
  });
};

export const useGetEpisodeRateStatsQuery = (
  episodeId: string,
  options?: Partial<UseQueryOptions<IGetEpisodeRateStatsResponse, Error>>
) => {
  return useQuery<IGetEpisodeRateStatsResponse, Error>({
    queryKey: queryKeys.rates.episodeRateStats(episodeId),
    queryFn: () => getEpisodeRateStatsRequest(episodeId),
    ...options,
  });
};

export const useGetEpisodeWatchListQuery = (
  episodeId: string,
  options?: Omit<UseQueryOptions<IEpisodeWatchlistResponse, Error>, 'queryKey'>
) => {
  return useQuery<IEpisodeWatchlistResponse, Error>({
    queryKey: queryKeys.watchlist.getEpisodeWatchlistRequest(episodeId),
    enabled: !!episodeId,
    refetchOnMount: true,
    queryFn: () => getEpisodeWatchlistRequest(episodeId),
    ...options,
  });
};

export const useGetEpisodeMarkAsWatchQuery = (
  episodeId: string,
  options?: Partial<UseQueryOptions<IEpisodeMarkAsWatchResponse, Error>>
) => {
  return useQuery<IEpisodeMarkAsWatchResponse, Error>({
    queryKey: queryKeys.markAsWatched.getEpisodeMarkAsWatchedRequest(episodeId),
    refetchOnMount: true,
    queryFn: () => getEpisodeMarkAsWatchedRequest(episodeId),
    ...options,
  });
};
