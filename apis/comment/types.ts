import { IPaginationResponse, IQueryParams, ISource } from '../params';

export interface IComment {
  id: string;
  title: string;
  content: string;
  images: string[];
  likeCount: string;
  replyCount: string;
  user: {
    id: number;
    username: string;
    avatar: string;
  };
  hasLiked: boolean;
  podcast: string;
  createdAt: string;
  updatedAt: string;
}

export interface ICommentEpisode {
  id: string;
  title: string;
  content: string;
  images: any[];
  createdAt: string;
  updatedAt: string;
  isEdited: boolean;
  likeCount: string;
  replyCount: string;
  episode: string;
  hasLiked: boolean;
  user: {
    id: number;
    username: string;
    avatar: string;
  };
}

export interface ICreateCommentParams {
  podcastId: string;
  content: string;
  title: string;
  images?: string[];
}

export interface ICreateCommentEpisodeParams {
  episodeId: string;
  content: string;
  title: string;
  images?: string[];
}

export interface IUploadCommentImageParams {
  count: number;
}

export interface IUploadCommentImageResponse {
  url: string;
  presigned: string;
}

export interface IGetCommentsParams extends IQueryParams {
  podcastId?: string;
}

export interface IGetEpisodeCommentsParams extends IQueryParams {
  episodeId?: string;
}

export interface ICommentCommunity {
  id: string;
  title: string;
  content: string;
  images: string[];
  createdAt: string;
  updatedAt: string;
  podcastId: string;
  isEdited: boolean;
  likeCount: string;
  replyCount: string;
  upVotes: string;
  downVotes: string;
  userVoteDirection: 'up' | 'down' | null;
  source: ISource;
  type: 'podcast' | 'episode';
  parentTitle: string;
  parentId: string;
  user: {
    id: number;
    username: string;
    avatar: string;
  };
  hasLiked: boolean;
  page: number;
  limit: number;
  tag: string;
  hasReported: boolean;
}

export interface IGetCommentsResponse extends IPaginationResponse<ICommentCommunity> {}

export interface IGetEpisodeCommentsResponse extends IPaginationResponse<ICommentCommunity> {}

export interface IDeleteCommentParams {
  id: string;
}

export interface ICommentResponse extends ICommentCommunity {}
export interface ICommentEpisodeResponse extends ICommentCommunity {}

export interface IUpdateCommentParams {
  podcastId: string;
  content: string;
  title: string;
  images?: string[];
  id: string;
}

export interface IUpdateEpisodeCommentParams {
  episodeId: string;
  content: string;
  title: string;
  images?: string[];
  id: string;
}

export interface IGetCommentReplyListParams extends IQueryParams {
  commentId: string;
  source: ISource;
}

export interface ICommentReplyListResponse extends IPaginationResponse<ICommentReply> {}

export interface IReplyPostPayload {
  commentId: number;
  content: string;
  source: ISource;
}

export interface IEditReplyPostPayload extends IReplyPostPayload {
  id: string;
}

export interface ICommentReply {
  commentId: number;
  user: {
    id: number;
    username: string;
    avatar: string;
  };
  content: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: any;
  hasReported: boolean;
  id: string;
}

export interface ILikeCommentResponse {
  liked: boolean;
}

export interface IVoteCommentRequest {
  commentId: string;
  vote: 'up' | 'down';
  source: ISource;
}

export interface IVoteCommentResponse {
  voted: 'up' | 'down' | null;
}

export interface IReportCommentParams {
  commentId: string;
  source: ISource;
  reasons: string[];
}

export interface IReportReplyCommentParams {
  replyId: string;
  source: ISource;
  reasons: string[];
}
