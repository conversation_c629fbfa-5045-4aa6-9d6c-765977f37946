import { InfiniteData, QueryClient, UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ISource } from '../params';
import {
  createCommentEpisodeRequest,
  createCommentRequest,
  deleteCommentRequest,
  deleteEpisodeCommentRequest,
  deleteReplyCommentEpisodeRequest,
  deleteReplyCommentRequest,
  editReplyCommentEpisodeRequest,
  editReplyCommentRequest,
  likeCommentRequest,
  likeEpisodeCommentRequest,
  replyCommentEpisodeRequest,
  replyCommentRequest,
  reportCommentRequest,
  reportEpisodeCommentRequest,
  reportReplyCommentRequest,
  reportReplyEpisodeCommentRequest,
  updateCommentRequest,
  updateEpisodeCommentRequest,
  uploadCommentImageRequest,
  voteCommentRequest,
  voteEpisodeCommentRequest,
} from './requests';
import {
  ICommentCommunity,
  ICommentEpisodeResponse,
  ICommentReply,
  ICommentResponse,
  ICreateCommentEpisodeParams,
  ICreateCommentParams,
  IDeleteCommentParams,
  IEditReplyPostPayload,
  IGetCommentsResponse,
  ILikeCommentResponse,
  IReplyPostPayload,
  IReportCommentParams,
  IReportReplyCommentParams,
  IUpdateCommentParams,
  IUpdateEpisodeCommentParams,
  IUploadCommentImageParams,
  IUploadCommentImageResponse,
  IVoteCommentRequest,
  IVoteCommentResponse,
} from './types';
import queryKeys from '@/utils/queryKeys';
import { handleToggleLikePost } from '../settled-handler/like';
import {
  createReplyCommentCache,
  deleteCommentCache,
  deleteReplyCommentCache,
  handleCreateComment,
  reportCommentCache,
  reportReplyCommentCache,
  updateCommentCache,
  updateReplyCommentCache,
} from '../settled-handler/comment';

// Create comment mutation
export const useCreateCommentMutation = (
  options?: UseMutationOptions<ICommentResponse, AxiosError, ICreateCommentParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: ICreateCommentParams) => createCommentRequest(params),
    onSettled: async (data, error, variables) => {
      await handleCreateComment(queryClient, error);
    },
    ...options,
  });
};

export const useCreateCommentEpisodeMutation = (
  options?: UseMutationOptions<ICommentEpisodeResponse, AxiosError, ICreateCommentEpisodeParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: ICreateCommentEpisodeParams) => createCommentEpisodeRequest(params),
    onSettled: async (data, error, variables) => {
      await handleCreateComment(queryClient, error);
    },
    ...options,
  });
};

// Upload comment image mutation
export const useUploadCommentImageMutation = (
  options?: UseMutationOptions<IUploadCommentImageResponse[], AxiosError, IUploadCommentImageParams>
) => {
  return useMutation({
    mutationFn: (params: IUploadCommentImageParams) => uploadCommentImageRequest(params),
    ...options,
  });
};

// Delete comment mutation
export const useDeleteCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IDeleteCommentParams,
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotPostUserHistory: any;
      snapshotReplyUserHistory: any;
      snapshotUserUpVoteHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteCommentParams) => deleteCommentRequest(params),
    async onMutate(variables) {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
        snapshotUserUpVoteHistory,
      } = await deleteCommentCache(queryClient, { id: variables.id });

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
        snapshotUserUpVoteHistory,
      };
    },
    async onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotPostUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotUserUpVoteHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useUpdateCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IUpdateCommentParams,
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
      snapshotCommentEpisodeItem: any;
      snapshotPostUserHistory: any;
      snapshotReplyUserHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCommentRequest,
    onMutate: async (variables) => {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
      } = await updateCommentCache(queryClient, variables);

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
      };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotPostUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useReplyCommentMutation = (
  options?: UseMutationOptions<
    ICommentReply,
    AxiosError,
    IReplyPostPayload,
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
      snapshotCommentEpisodeItem: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IReplyPostPayload) => replyCommentRequest(params),
    async onMutate(variables) {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
      } = await createReplyCommentCache(queryClient, {
        commentId: variables.commentId?.toString(),
        isEpisode: false,
      });

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
      };
    },
    onSettled: async (data, error, variables, context) => {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      await Promise.all([
        queryClient.resetQueries({ queryKey: queryKeys.commentsReply.list() }),
        queryClient.resetQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() }),
      ]);
    },
    ...options,
  });
};

export const useEditReplyCommentMutation = (
  options?: UseMutationOptions<
    ICommentReply,
    AxiosError,
    IEditReplyPostPayload,
    {
      snapshotCommentsReply: any;
      snapshotReplyUserHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IEditReplyPostPayload) => editReplyCommentRequest(params),
    async onMutate(variables) {
      const { snapshotCommentsReply, snapshotReplyUserHistory } = await updateReplyCommentCache(queryClient, {
        id: variables.id,
        content: variables.content,
        isEpisode: false,
      });

      return { snapshotCommentsReply, snapshotReplyUserHistory };
    },
    onSettled: (data, error, variables, context) => {
      if (error) {
        context?.snapshotCommentsReply?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useEditReplyCommentEpisodeMutation = (
  options?: UseMutationOptions<
    ICommentReply,
    AxiosError,
    IEditReplyPostPayload,
    {
      snapshotCommentsReplyEpisode: any;
      snapshotReplyUserHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IEditReplyPostPayload) => editReplyCommentEpisodeRequest(params),
    async onMutate(variables) {
      const { snapshotCommentsReplyEpisode, snapshotReplyUserHistory } = await updateReplyCommentCache(queryClient, {
        id: variables.id,
        content: variables.content,
        isEpisode: true,
      });

      return { snapshotCommentsReplyEpisode, snapshotReplyUserHistory };
    },
    onSettled: (data, error, variables, context) => {
      if (error) {
        context?.snapshotCommentsReplyEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useReplyCommentEpisodeMutation = (
  options?: UseMutationOptions<
    ICommentReply,
    AxiosError,
    IReplyPostPayload,
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
      snapshotCommentEpisodeItem: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IReplyPostPayload) => replyCommentEpisodeRequest(params),
    async onMutate(variables) {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
      } = await createReplyCommentCache(queryClient, {
        commentId: variables.commentId?.toString(),
        isEpisode: true,
      });

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
      };
    },
    onSettled: async (data, error, variables, context) => {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      await Promise.all([
        queryClient.resetQueries({ queryKey: queryKeys.commentsReply.listEpisode() }),
        queryClient.resetQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() }),
      ]);
    },
    ...options,
  });
};

export const useDeleteReplyCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    { id: string; source: ISource; commentId: string },
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
      snapshotCommentEpisodeItem: any;
      snapshotCommentsReplyEpisode: any;
      snapshotCommentsReply: any;
      snapshotReplyUserHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteReplyCommentRequest,
    async onMutate(variables) {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotCommentsReplyEpisode,
        snapshotCommentsReply,
        snapshotReplyUserHistory,
      } = await deleteReplyCommentCache(queryClient, {
        id: variables.id,
        isEpisode: false,
        commentId: variables.commentId,
      });

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotCommentsReplyEpisode,
        snapshotCommentsReply,
        snapshotReplyUserHistory,
      };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsReply?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsReplyEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useDeleteReplyCommentEpisodeMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    { id: string; source: ISource; commentId: string },
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
      snapshotCommentEpisodeItem: any;
      snapshotCommentsReplyEpisode: any;
      snapshotCommentsReply: any;
      snapshotReplyUserHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteReplyCommentEpisodeRequest,
    async onMutate(variables) {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotCommentsReplyEpisode,
        snapshotCommentsReply,
        snapshotReplyUserHistory,
      } = await deleteReplyCommentCache(queryClient, {
        id: variables.id,
        isEpisode: true,
        commentId: variables.commentId,
      });

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotCommentsReplyEpisode,
        snapshotCommentsReply,
        snapshotReplyUserHistory,
      };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsReply?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsReplyEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

const likeCommentUpdateCache = async (queryClient: QueryClient, commentId: string) => {
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  const snapshotCommentsShowDetail = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.list(),
  });

  // infinite comment show detail
  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    return {
      ...oldData,
      pages: oldData.pages.map((page) => ({
        ...page,
        data: page.data.map((comment) =>
          comment.id === commentId
            ? {
                ...comment,
                hasLiked: !comment.hasLiked,
                likeCount: comment.hasLiked
                  ? (Number(comment.likeCount) - 1).toString()
                  : (Number(comment.likeCount) + 1).toString(),
              }
            : comment
        ),
      })),
    };
  });

  await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

  const snapshotCommentPodcastItem = queryClient.getQueriesData<ICommentResponse>({
    queryKey: queryKeys.comments.item(),
  });

  // podcast comment detail
  queryClient.setQueriesData<ICommentResponse>(
    {
      queryKey: queryKeys.comments.item(),
    },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        hasLiked: !oldData.hasLiked,
        likeCount: oldData.hasLiked
          ? (Number(oldData.likeCount) - 1).toString()
          : (Number(oldData.likeCount) + 1).toString(),
      };
    }
  );

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  const snapshotCommentsEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.listEpisode(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData.pages.map((page) => ({
          ...page,
          data: page.data.map((comment) =>
            comment.id === commentId
              ? {
                  ...comment,
                  hasLiked: !comment.hasLiked,
                  likeCount: comment.hasLiked
                    ? (Number(comment.likeCount) - 1).toString()
                    : (Number(comment.likeCount) + 1).toString(),
                }
              : comment
          ),
        })),
      };
    }
  );

  // episode comment detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode() });

  const snapshotCommentEpisodeItem = queryClient.getQueriesData<ICommentResponse>({
    queryKey: queryKeys.comments.itemEpisode(),
  });

  queryClient.setQueriesData<ICommentResponse>(
    {
      queryKey: queryKeys.comments.itemEpisode(),
    },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        hasLiked: !oldData.hasLiked,
        likeCount: oldData.hasLiked
          ? (Number(oldData.likeCount) - 1).toString()
          : (Number(oldData.likeCount) + 1).toString(),
      };
    }
  );

  return {
    snapshotCommentsShowDetail,
    snapshotCommentPodcastItem,
    snapshotCommentsEpisode,
    snapshotCommentEpisodeItem,
  };
};

export const useLikeCommentMutation = (
  options?: UseMutationOptions<
    ILikeCommentResponse,
    AxiosError,
    { id: string; source: ISource },
    { snapshotCommentsShowDetail: any; snapshotCommentPodcastItem: any }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => likeCommentRequest(payload),
    onMutate: async ({ id, source }) => {
      const { snapshotCommentsShowDetail, snapshotCommentPodcastItem } = await likeCommentUpdateCache(queryClient, id);

      return { snapshotCommentsShowDetail, snapshotCommentPodcastItem };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      handleToggleLikePost(queryClient, error);
    },
    ...options,
  });
};

// Episode comment mutations
export const useDeleteEpisodeCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IDeleteCommentParams,
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotPostUserHistory: any;
      snapshotReplyUserHistory: any;
      snapshotUserUpVoteHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteCommentParams) => deleteEpisodeCommentRequest(params),
    async onMutate(variables) {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
        snapshotUserUpVoteHistory,
      } = await deleteCommentCache(queryClient, { id: variables.id, isEpisode: true });

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
        snapshotUserUpVoteHistory,
      };
    },
    async onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotPostUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotUserUpVoteHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useLikeEpisodeCommentMutation = (
  options?: UseMutationOptions<
    ILikeCommentResponse,
    AxiosError,
    { id: string; source: ISource },
    {
      snapshotCommentEpisodeItem: any;
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => likeEpisodeCommentRequest(payload),
    onMutate: async (variables) => {
      const { snapshotCommentEpisodeItem, snapshotCommentsShowDetail, snapshotCommentsEpisode } =
        await likeCommentUpdateCache(queryClient, variables.id);

      return { snapshotCommentEpisodeItem, snapshotCommentsShowDetail, snapshotCommentsEpisode };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      handleToggleLikePost(queryClient, error);
    },
    ...options,
  });
};

export const useUpdateEpisodeCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IUpdateEpisodeCommentParams,
    {
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
      snapshotCommentEpisodeItem: any;
      snapshotPostUserHistory: any;
      snapshotReplyUserHistory: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateEpisodeCommentRequest,
    onMutate: async (variables) => {
      const {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
      } = await updateCommentCache(queryClient, variables);

      return {
        snapshotCommentsShowDetail,
        snapshotCommentsEpisode,
        snapshotCommentPodcastItem,
        snapshotCommentEpisodeItem,
        snapshotPostUserHistory,
        snapshotReplyUserHistory,
      };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotCommentsShowDetail?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentsEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentPodcastItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotCommentEpisodeItem?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotPostUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotReplyUserHistory?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

const updateVoteComment = (comment: ICommentCommunity, variables: IVoteCommentRequest) => {
  const newData = { ...comment };

  if (!comment.userVoteDirection) {
    if (variables.vote === 'up') {
      newData.upVotes = (Number(comment?.upVotes) + 1).toString();
    } else {
      newData.downVotes = (Number(comment?.downVotes) + 1).toString();
    }
    newData.userVoteDirection = variables.vote;
  } else if (comment.userVoteDirection === variables.vote) {
    if (variables.vote === 'up') {
      newData.upVotes = (Number(comment?.upVotes) - 1).toString();
    } else {
      newData.downVotes = (Number(comment?.downVotes) - 1).toString();
    }
    newData.userVoteDirection = null;
  } else {
    if (variables.vote === 'up') {
      newData.upVotes = (Number(comment?.upVotes) + 1).toString();
      newData.downVotes = (Number(comment?.downVotes) - 1).toString();
    } else {
      newData.upVotes = (Number(comment?.upVotes) - 1).toString();
      newData.downVotes = (Number(comment?.downVotes) + 1).toString();
    }
    newData.userVoteDirection = variables.vote;
  }

  return newData;
};

export const useVoteCommentMutation = (
  options?: UseMutationOptions<IVoteCommentResponse, AxiosError, IVoteCommentRequest, { snapshot: any }>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => voteCommentRequest(payload),
    onMutate: async (variables) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

      const snapshot = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
        queryKey: queryKeys.comments.list(),
      });

      queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
        {
          queryKey: queryKeys.comments.list(),
        },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => ({
              ...page,
              data: page.data.map((comment) => {
                if (comment.id !== variables.commentId) return comment;
                return updateVoteComment(comment, variables);
              }),
            })),
          };
        }
      );

      return { snapshot };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshot?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useVoteEpisodeCommentMutation = (
  options?: UseMutationOptions<
    IVoteCommentResponse,
    AxiosError,
    IVoteCommentRequest,
    { snapshotPodcast: any; snapshotEpisode: any }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => voteEpisodeCommentRequest(payload),
    onMutate: async (variables) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

      const snapshotEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
        queryKey: queryKeys.comments.listEpisode(),
      });

      queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
        { queryKey: queryKeys.comments.listEpisode() },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => ({
              ...page,
              data: page.data.map((comment) => {
                if (comment.id !== variables.commentId) return comment;
                return updateVoteComment(comment, variables);
              }),
            })),
          };
        }
      );

      const snapshotPodcast = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
        queryKey: queryKeys.comments.list(),
      });

      queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
        {
          queryKey: queryKeys.comments.list(),
        },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => ({
              ...page,
              data: page.data.map((comment) => {
                if (comment.id !== variables.commentId) return comment;
                return updateVoteComment(comment, variables);
              }),
            })),
          };
        }
      );

      return { snapshotEpisode, snapshotPodcast };
    },
    onSettled(data, error, variables, context) {
      if (error) {
        context?.snapshotEpisode?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
        context?.snapshotPodcast?.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    ...options,
  });
};

export const useReportCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IReportCommentParams,
    {
      snapshotCommentEpisodeItem: any;
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => reportCommentRequest(payload),
    async onSettled(data, error, variables, context) {
      if (!error) {
        await reportCommentCache(queryClient, {
          id: variables.commentId,
          isEpisode: true,
        });
      }
    },
    ...options,
  });
};

export const useReportEpisodeCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IReportCommentParams,
    {
      snapshotCommentEpisodeItem: any;
      snapshotCommentsShowDetail: any;
      snapshotCommentsEpisode: any;
      snapshotCommentPodcastItem: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => reportEpisodeCommentRequest(payload),
    async onSettled(data, error, variables, context) {
      if (!error) {
        await reportCommentCache(queryClient, {
          id: variables.commentId,
          isEpisode: true,
        });
      }
    },
    ...options,
  });
};

export const useReportReplyCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IReportReplyCommentParams,
    {
      snapshotCommentsReply: any;
      snapshotCommentsReplyEpisode: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => reportReplyCommentRequest(payload),
    async onSettled(data, error, variables, context) {
      if (!error) {
        await reportReplyCommentCache(queryClient, {
          id: variables.replyId,
          isEpisode: false,
        });
      }
    },
    ...options,
  });
};

export const useReportReplyEpisodeCommentMutation = (
  options?: UseMutationOptions<
    void,
    AxiosError,
    IReportReplyCommentParams,
    {
      snapshotCommentsReply: any;
      snapshotCommentsReplyEpisode: any;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload) => reportReplyEpisodeCommentRequest(payload),
    async onSettled(data, error, variables, context) {
      if (!error) {
        await reportReplyCommentCache(queryClient, {
          id: variables.replyId,
          isEpisode: true,
        });
      }
    },
    ...options,
  });
};
