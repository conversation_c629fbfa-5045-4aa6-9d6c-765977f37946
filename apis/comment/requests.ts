import { env } from '@/utils/const';
import { request } from '../axios';
import { ISource } from '../params';
import {
  ICommentEpisodeResponse,
  ICommentReply,
  ICommentReplyListResponse,
  ICommentResponse,
  ICreateCommentEpisodeParams,
  ICreateCommentParams,
  IDeleteCommentParams,
  IEditReplyPostPayload,
  IGetCommentReplyListParams,
  IGetCommentsParams,
  IGetCommentsResponse,
  IGetEpisodeCommentsParams,
  IGetEpisodeCommentsResponse,
  ILikeCommentResponse,
  IReplyPostPayload,
  IReportCommentParams,
  IReportReplyCommentParams,
  IUpdateCommentParams,
  IUpdateEpisodeCommentParams,
  IUploadCommentImageParams,
  IUploadCommentImageResponse,
  IVoteCommentRequest,
  IVoteCommentResponse,
} from './types';

export const getCommentsRequest = async (params: IGetCommentsParams): Promise<IGetCommentsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast/community/${params.podcastId}`,
    method: 'GET',
    params,
  });

  const comments = data.data as IGetCommentsResponse['data'];

  return {
    ...data,
    data: comments.filter((comment) => !comment.hasReported),
  };
};

export const getCommentByIdRequest = async (id: string, source: 'local' | 'podchaser'): Promise<ICommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/${id}/${source}`,
    method: 'GET',
  });

  return data.data;
};

export const getEpisodeCommentByIdRequest = async (
  id: string,
  source: 'local' | 'podchaser'
): Promise<ICommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment/${id}/${source}`,
    method: 'GET',
  });

  return data.data;
};

export const getCommentReplyListRequest = async (
  params: IGetCommentReplyListParams
): Promise<ICommentReplyListResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply`,
    method: 'GET',
    params,
  });

  const filteredData = data.data as ICommentReplyListResponse['data'];

  return {
    ...data,
    data: filteredData.filter((reply) => !reply.hasReported),
  };
};

export const getCommentEpisodeReplyListRequest = async (
  params: IGetCommentReplyListParams
): Promise<ICommentReplyListResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply`,
    method: 'GET',
    params,
  });

  const filteredData = data.data as ICommentReplyListResponse['data'];

  return {
    ...data,
    data: filteredData.filter((reply) => !reply.hasReported),
  };
};

export const createCommentRequest = async (params: ICreateCommentParams): Promise<ICommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment`,
    method: 'POST',
    data: params,
  });

  return data;
};

export const createCommentEpisodeRequest = async (
  params: ICreateCommentEpisodeParams
): Promise<ICommentEpisodeResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment`,
    method: 'POST',
    data: params,
  });

  return data;
};

export const uploadCommentImageRequest = async (
  params: IUploadCommentImageParams
): Promise<IUploadCommentImageResponse[]> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/upload`,
    method: 'POST',
    data: params,
  });

  return data.data;
};

export const deleteCommentRequest = async (params: IDeleteCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast-comment/${params.id}`,
    method: 'DELETE',
  });
};

export const updateCommentRequest = async ({ id, ...rest }: IUpdateCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast-comment/${id}`,
    method: 'PATCH',
    data: rest,
  });
};

export const replyCommentRequest = async (params: IReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply`,
    method: 'POST',
    data: params,
  });

  return data?.data;
};

export const editReplyCommentRequest = async ({ id, ...params }: IEditReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply/${id}`,
    method: 'PATCH',
    data: params,
  });

  return data?.data;
};

export const editReplyCommentEpisodeRequest = async ({
  id,
  ...params
}: IEditReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply/${id}`,
    method: 'PATCH',
    data: params,
  });

  return data?.data;
};

export const replyCommentEpisodeRequest = async (params: IReplyPostPayload): Promise<ICommentReply> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply`,
    method: 'POST',
    data: params,
  });

  return data?.data;
};

export const deleteReplyCommentRequest = async ({ id, source }: { id: string; source: ISource }): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast-comment-reply/${id}/${source}`,
    method: 'DELETE',
  });
};

export const deleteReplyCommentEpisodeRequest = async ({
  id,
  source,
}: { id: string; source: ISource }): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-comment-reply/${id}/${source}`,
    method: 'DELETE',
  });
};

export const likeCommentRequest = async ({
  id,
  source,
}: { id: string; source: ISource }): Promise<ILikeCommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/${id}/like/${source}`,
    method: 'POST',
  });

  return data?.data;
};

export const getEpisodeCommentsRequest = async (
  params: IGetEpisodeCommentsParams
): Promise<IGetEpisodeCommentsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment`,
    method: 'GET',
    params,
  });

  const comments = data.data as IGetEpisodeCommentsResponse['data'];

  return {
    ...data,
    data: comments.filter((comment) => !comment.hasReported),
  };
};

export const deleteEpisodeCommentRequest = async (params: IDeleteCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-comment/${params.id}`,
    method: 'DELETE',
  });
};

export const likeEpisodeCommentRequest = async ({
  id,
  source,
}: { id: string; source: ISource }): Promise<ILikeCommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment/${id}/like/${source}`,
    method: 'POST',
  });

  return data?.data;
};

export const updateEpisodeCommentRequest = async ({ id, ...rest }: IUpdateEpisodeCommentParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-comment/${id}`,
    method: 'PATCH',
    data: rest,
  });
};

export const voteCommentRequest = async ({
  commentId,
  source,
  vote,
}: IVoteCommentRequest): Promise<IVoteCommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/vote`,
    method: 'POST',
    data: { commentId, source, vote },
  });

  return data;
};

export const voteEpisodeCommentRequest = async ({
  commentId,
  source,
  vote,
}: IVoteCommentRequest): Promise<IVoteCommentResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment/vote`,
    method: 'POST',
    data: { commentId, source, vote },
  });

  return data;
};

export const reportCommentRequest = async ({ commentId, source, reasons }: IReportCommentParams): Promise<void> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment/report`,
    method: 'POST',
    data: { commentId, source, reasons },
  });

  return data;
};

export const reportEpisodeCommentRequest = async ({
  commentId,
  source,
  reasons,
}: IReportCommentParams): Promise<void> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment/report`,
    method: 'POST',
    data: { commentId, source, reasons },
  });

  return data;
};

export const reportReplyCommentRequest = async ({
  replyId,
  source,
  reasons,
}: IReportReplyCommentParams): Promise<void> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-comment-reply/report`,
    method: 'POST',
    data: { replyId, source, reasons },
  });

  return data;
};

export const reportReplyEpisodeCommentRequest = async ({
  replyId,
  source,
  reasons,
}: IReportReplyCommentParams): Promise<void> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-comment-reply/report`,
    method: 'POST',
    data: { replyId, source, reasons },
  });

  return data;
};
