import { useQuery, UseQueryOptions, useInfiniteQuery, UseInfiniteQueryOptions } from '@tanstack/react-query';
import { IFollowRequest, IGetFollowersResponse, IGetFollowingResponse } from './types';
import { getFollowersRequest, getFollowingRequest } from './requests';
import queryKeys from '../../utils/queryKeys';

// Get Followers Query
export const useGetFollowersQuery = (
  params: IFollowRequest = {},
  options?: UseQueryOptions<IGetFollowersResponse, Error>
) => {
  return useQuery({
    queryKey: queryKeys.follow.followersList(params),
    queryFn: () => getFollowersRequest(params),
    staleTime: 1000 * 60 * 5,
    refetchOnMount: true,
    ...options,
  });
};

// Infinite Followers Query
export const useGetFollowersInfiniteQuery = (
  params: Omit<IFollowRequest, 'page'> = {},
  options?: UseInfiniteQueryOptions<IGetFollowersResponse, Error>
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.follow.followersInfinite(params),
    queryFn: ({ pageParam = 1 }) => getFollowersRequest({ ...params, page: pageParam as number }),
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.currentPage < lastPage.pagination.totalPages
        ? lastPage.pagination.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
    refetchOnMount: true,
    ...options,
  });
};

// Get Following Query
export const useGetFollowingQuery = (
  params: IFollowRequest = {},
  options?: UseQueryOptions<IGetFollowingResponse, Error>
) => {
  return useQuery({
    queryKey: queryKeys.follow.followingList(params),
    queryFn: () => getFollowingRequest(params),
    staleTime: 1000 * 60 * 5,
    refetchOnMount: true,
    ...options,
  });
};

// Infinite Following Query
export const useGetFollowingInfiniteQuery = (
  params: Omit<IFollowRequest, 'page'> = {},
  options?: UseInfiniteQueryOptions<IGetFollowingResponse, Error>
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.follow.followingInfinite(params),
    queryFn: ({ pageParam = 1 }) => getFollowingRequest({ ...params, page: pageParam as number }),
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.currentPage < lastPage.pagination.totalPages
        ? lastPage.pagination.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
    refetchOnMount: true,
    ...options,
  });
};
