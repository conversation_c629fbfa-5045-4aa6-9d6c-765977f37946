import queryKeys from '@/utils/queryKeys';
import { InfiniteData, QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  ICommentEpisodeResponse,
  ICommentReplyListResponse,
  ICommentResponse,
  IGetCommentsResponse,
  IUpdateCommentParams,
} from '../comment/types';
import { IPostHistoryResponse, IGetReplyUserHistoryResponse, IGetUserUpVoteHistoryResponse } from '../user';
import { IGetCommunityFeedResponse } from '../podcast';

export const handleCreateComment = async (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  return await Promise.all([
    queryClient.resetQueries({ queryKey: queryKeys.comments.listEpisode() }),
    queryClient.resetQueries({ queryKey: queryKeys.comments.list() }),
    queryClient.resetQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() }),
  ]);
};

export const updateCommentCache = async (
  queryClient: QueryClient,
  variables: Pick<IUpdateCommentParams, 'id' | 'content' | 'title' | 'images'> & { episodeId?: string }
) => {
  const isUpdateCommentEpisode = !!variables.episodeId;

  // infinite comment show detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  const snapshotCommentsShowDetail = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.list(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    return {
      ...oldData,
      pages: oldData?.pages?.map((page) => ({
        ...page,
        data: page?.data?.map((comment) => {
          if (isUpdateCommentEpisode) {
            if (comment.type !== 'episode') return comment;
            return comment.id === variables.id
              ? {
                  ...comment,
                  content: variables.content,
                  title: variables?.title,
                  images: variables?.images || [],
                  updatedAt: new Date().toISOString(),
                  isEdited: true,
                }
              : comment;
          } else {
            if (comment.type !== 'podcast') return comment;
            return comment.id === variables.id
              ? {
                  ...comment,
                  content: variables.content,
                  title: variables?.title,
                  images: variables?.images || [],
                  updatedAt: new Date().toISOString(),
                  isEdited: true,
                }
              : comment;
          }
        }),
      })),
    };
  });

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  const snapshotCommentsEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.listEpisode(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables.id
              ? {
                  ...comment,
                  content: variables.content,
                  title: variables?.title,
                  images: variables?.images || [],
                  updatedAt: new Date().toISOString(),
                  isEdited: true,
                }
              : comment
          ),
        })),
      };
    }
  );

  // comment detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

  const snapshotCommentPodcastItem = queryClient.getQueriesData<ICommentResponse>({
    queryKey: queryKeys.comments.item(),
  });

  queryClient.setQueriesData<ICommentResponse>({ queryKey: queryKeys.comments.item() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.id || variables?.episodeId) return oldData;

    return {
      ...oldData,
      content: variables.content,
      title: variables?.title,
      images: variables?.images || [],
      updatedAt: new Date().toISOString(),
      isEdited: true,
    };
  });

  // episode comment detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode() });

  const snapshotCommentEpisodeItem = queryClient.getQueriesData<ICommentEpisodeResponse>({
    queryKey: queryKeys.comments.itemEpisode(),
  });

  queryClient.setQueriesData<ICommentEpisodeResponse>({ queryKey: queryKeys.comments.itemEpisode() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.id || !variables?.episodeId) return oldData;

    return {
      ...oldData,
      content: variables.content,
      title: variables?.title,
      images: variables?.images || [],
      updatedAt: new Date().toISOString(),
      isEdited: true,
    };
  });

  // post user history
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });

  const snapshotPostUserHistory = queryClient.getQueriesData<InfiniteData<IPostHistoryResponse>>({
    queryKey: queryKeys.userProfile.postUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.userProfile.postUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) => {
            if (isUpdateCommentEpisode) {
              if (comment.type !== 'episode') return comment;
              return comment.id === variables.id
                ? {
                    ...comment,
                    content: variables.content,
                    title: variables?.title,
                    images: variables?.images || [],
                    updatedAt: new Date().toISOString(),
                    isEdited: true,
                  }
                : comment;
            } else {
              if (comment.type !== 'podcast') return comment;
              return comment.id === variables.id
                ? {
                    ...comment,
                    content: variables.content,
                    title: variables?.title,
                    images: variables?.images || [],
                    updatedAt: new Date().toISOString(),
                    isEdited: true,
                  }
                : comment;
            }
          }),
        })),
      };
    }
  );

  // reply user history
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });

  const snapshotReplyUserHistory = queryClient.getQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>({
    queryKey: queryKeys.userProfile.replyUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
    { queryKey: queryKeys.userProfile.replyUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) => {
            return comment.commentId === variables.id &&
              comment.type === (isUpdateCommentEpisode ? 'episode' : 'podcast')
              ? {
                  ...comment,
                  commentTitle: variables?.title,
                  commentContent: variables?.content,
                  updatedAt: new Date().toISOString(),
                  isEdited: true,
                }
              : comment;
          }),
        })),
      };
    }
  );

  return {
    snapshotCommentsShowDetail,
    snapshotCommentsEpisode,
    snapshotCommentPodcastItem,
    snapshotCommentEpisodeItem,
    snapshotPostUserHistory,
    snapshotReplyUserHistory,
  };
};

export const deleteCommentCache = async (queryClient: QueryClient, variables: { id: string; isEpisode?: boolean }) => {
  // infinite comment show detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  const snapshotCommentsShowDetail = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.list(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    return {
      ...oldData,
      pages: oldData?.pages?.map((page) => ({
        ...page,
        data: page.data.filter((comment) => {
          if (variables.isEpisode) {
            if (comment.type !== 'episode') return true;
            return comment.id !== variables.id;
          } else {
            if (comment.type !== 'podcast') return true;
            return comment.id !== variables.id;
          }
        }),
      })),
    };
  });

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  const snapshotCommentsEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.listEpisode(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => comment.id !== variables.id),
        })),
      };
    }
  );

  // history post
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });

  const snapshotPostUserHistory = queryClient.getQueriesData<InfiniteData<IPostHistoryResponse>>({
    queryKey: queryKeys.userProfile.postUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IPostHistoryResponse>>(
    { queryKey: queryKeys.userProfile.postUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => {
            if (variables.isEpisode) {
              return comment.type !== 'episode' || comment.id !== variables.id;
            } else {
              return comment.type !== 'podcast' || comment.id !== variables.id;
            }
          }),
        })),
      };
    }
  );

  // history reply
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });

  const snapshotReplyUserHistory = queryClient.getQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>({
    queryKey: queryKeys.userProfile.replyUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
    { queryKey: queryKeys.userProfile.replyUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => comment.commentId !== variables.id),
        })),
      };
    }
  );

  // history upvote
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.getUserUpVoteHistoryInfinite() });

  const snapshotUserUpVoteHistory = queryClient.getQueriesData<InfiniteData<IGetUserUpVoteHistoryResponse>>({
    queryKey: queryKeys.userProfile.getUserUpVoteHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetUserUpVoteHistoryResponse>>(
    { queryKey: queryKeys.userProfile.getUserUpVoteHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => {
            if (variables.isEpisode) {
              return comment.type !== 'episode' || comment.commentId !== variables.id;
            } else {
              return comment.type !== 'podcast' || comment.commentId !== variables.id;
            }
          }),
        })),
      };
    }
  );

  return {
    snapshotCommentsShowDetail,
    snapshotCommentsEpisode,
    snapshotPostUserHistory,
    snapshotReplyUserHistory,
    snapshotUserUpVoteHistory,
  };
};

export const createReplyCommentCache = async (
  queryClient: QueryClient,
  variables: {
    isEpisode?: boolean;
    commentId: string;
  }
) => {
  // infinite comment show detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  const snapshotCommentsShowDetail = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.list(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    return {
      ...oldData,
      pages: oldData?.pages?.map((page) => ({
        ...page,
        data: page?.data?.map((comment) => {
          if (variables.isEpisode) {
            if (comment.type !== 'episode') return comment;
            return comment.id === variables.commentId
              ? {
                  ...comment,
                  replyCount: (Number(comment.replyCount) + 1).toString(),
                }
              : comment;
          } else {
            if (comment.type !== 'podcast') return comment;
            return comment.id === variables.commentId
              ? {
                  ...comment,
                  replyCount: (Number(comment.replyCount) + 1).toString(),
                }
              : comment;
          }
        }),
      })),
    };
  });

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  const snapshotCommentsEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.listEpisode(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables.commentId
              ? {
                  ...comment,
                  replyCount: (Number(comment.replyCount) + 1).toString(),
                }
              : comment
          ),
        })),
      };
    }
  );

  // comment podcast detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

  const snapshotCommentPodcastItem = queryClient.getQueriesData<ICommentResponse>({
    queryKey: queryKeys.comments.item(),
  });

  queryClient.setQueriesData<ICommentResponse>({ queryKey: queryKeys.comments.item() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.commentId || variables.isEpisode) return oldData;

    return {
      ...oldData,
      replyCount: (Number(oldData.replyCount) + 1).toString(),
    };
  });

  // comment episode detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode() });

  const snapshotCommentEpisodeItem = queryClient.getQueriesData<ICommentEpisodeResponse>({
    queryKey: queryKeys.comments.itemEpisode(),
  });

  queryClient.setQueriesData<ICommentEpisodeResponse>({ queryKey: queryKeys.comments.itemEpisode() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.commentId || !variables.isEpisode) return oldData;

    return {
      ...oldData,
      replyCount: (Number(oldData.replyCount) + 1).toString(),
    };
  });

  return {
    snapshotCommentsShowDetail,
    snapshotCommentsEpisode,
    snapshotCommentPodcastItem,
    snapshotCommentEpisodeItem,
  };
};

export const updateReplyCommentCache = async (
  queryClient: QueryClient,
  variables: { id: string; content: string; isEpisode?: boolean }
) => {
  await queryClient.cancelQueries({ queryKey: queryKeys.commentsReply.listEpisode() });
  await queryClient.cancelQueries({ queryKey: queryKeys.commentsReply.list() });

  let snapshotCommentsReplyEpisode = queryClient.getQueriesData<InfiniteData<ICommentReplyListResponse>>({
    queryKey: queryKeys.commentsReply.listEpisode(),
  });

  let snapshotCommentsReply = queryClient.getQueriesData<InfiniteData<ICommentReplyListResponse>>({
    queryKey: queryKeys.commentsReply.list(),
  });

  if (variables.isEpisode) {
    queryClient.setQueriesData<InfiniteData<ICommentReplyListResponse>>(
      { queryKey: queryKeys.commentsReply.listEpisode() },
      (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData?.pages?.map((page) => ({
            ...page,
            data: page?.data?.map((comment) =>
              comment.id === variables.id
                ? {
                    ...comment,
                    content: variables.content,
                    updatedAt: new Date().toISOString(),
                    isEdited: true,
                  }
                : comment
            ),
          })),
        };
      }
    );
  } else {
    queryClient.setQueriesData<InfiniteData<ICommentReplyListResponse>>(
      { queryKey: queryKeys.commentsReply.list() },
      (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData?.pages?.map((page) => ({
            ...page,
            data: page?.data?.map((comment) =>
              comment.id === variables.id
                ? {
                    ...comment,
                    content: variables.content,
                    updatedAt: new Date().toISOString(),
                    isEdited: true,
                  }
                : comment
            ),
          })),
        };
      }
    );
  }

  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });

  const snapshotReplyUserHistory = queryClient.getQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>({
    queryKey: queryKeys.userProfile.replyUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
    { queryKey: queryKeys.userProfile.replyUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables.id
              ? {
                  ...comment,
                  content: variables.content,
                  updatedAt: new Date().toISOString(),
                  isEdited: true,
                }
              : comment
          ),
        })),
      };
    }
  );

  return {
    snapshotCommentsReplyEpisode,
    snapshotCommentsReply,
    snapshotReplyUserHistory,
  };
};

export const deleteReplyCommentCache = async (
  queryClient: QueryClient,
  variables: { id: string; isEpisode?: boolean; commentId: string }
) => {
  // infinite comment show detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  const snapshotCommentsShowDetail = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.list(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    return {
      ...oldData,
      pages: oldData?.pages?.map((page) => ({
        ...page,
        data: page?.data?.map((comment) => {
          return comment.id === variables?.commentId && comment.type === (variables.isEpisode ? 'episode' : 'podcast')
            ? { ...comment, replyCount: (Number(comment.replyCount) - 1).toString() }
            : comment;
        }),
      })),
    };
  });

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  const snapshotCommentsEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.listEpisode(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables?.commentId
              ? { ...comment, replyCount: (Number(comment.replyCount) - 1).toString() }
              : comment
          ),
        })),
      };
    }
  );

  // comment podcast detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

  const snapshotCommentPodcastItem = queryClient.getQueriesData<ICommentResponse>({
    queryKey: queryKeys.comments.item(),
  });

  queryClient.setQueriesData<ICommentResponse>({ queryKey: queryKeys.comments.item() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.commentId || variables.isEpisode) return oldData;

    return {
      ...oldData,
      replyCount: (Number(oldData.replyCount) - 1).toString(),
    };
  });

  // comment episode detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode() });

  const snapshotCommentEpisodeItem = queryClient.getQueriesData<ICommentEpisodeResponse>({
    queryKey: queryKeys.comments.itemEpisode(),
  });

  queryClient.setQueriesData<ICommentEpisodeResponse>({ queryKey: queryKeys.comments.itemEpisode() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.commentId || !variables.isEpisode) return oldData;

    return {
      ...oldData,
      replyCount: (Number(oldData.replyCount) - 1).toString(),
    };
  });

  // infinite comment reply
  await queryClient.cancelQueries({ queryKey: queryKeys.commentsReply.list() });

  const snapshotCommentsReply = queryClient.getQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>({
    queryKey: queryKeys.commentsReply.list(),
  });

  await queryClient.cancelQueries({ queryKey: queryKeys.commentsReply.listEpisode() });

  const snapshotCommentsReplyEpisode = queryClient.getQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>({
    queryKey: queryKeys.commentsReply.listEpisode(),
  });

  if (variables.isEpisode) {
    queryClient.setQueriesData<InfiniteData<ICommentReplyListResponse>>(
      { queryKey: queryKeys.commentsReply.listEpisode() },
      (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData?.pages?.map((page) => ({
            ...page,
            data: page.data.filter((comment) => comment.id !== variables.id),
          })),
        };
      }
    );
  } else {
    queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
      { queryKey: queryKeys.commentsReply.list() },
      (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData?.pages?.map((page) => ({
            ...page,
            data: page.data.filter((comment) => comment.id !== variables.id),
          })),
        };
      }
    );
  }

  // history reply
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });

  const snapshotReplyUserHistory = queryClient.getQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>({
    queryKey: queryKeys.userProfile.replyUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
    { queryKey: queryKeys.userProfile.replyUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => comment.id !== variables.id),
        })),
      };
    }
  );

  return {
    snapshotCommentsShowDetail,
    snapshotCommentsEpisode,
    snapshotCommentPodcastItem,
    snapshotCommentEpisodeItem,
    snapshotCommentsReplyEpisode,
    snapshotCommentsReply,
    snapshotReplyUserHistory,
  };
};

export const reportCommentCache = async (
  queryClient: QueryClient,
  variables: { id: string; isEpisode?: boolean; isReport?: boolean }
) => {
  const hasReported = variables?.isReport !== undefined ? variables?.isReport : true;

  // infinite comment show detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    return {
      ...oldData,
      pages: oldData?.pages?.map((page) => ({
        ...page,
        data: page?.data?.map((comment) =>
          comment.id === variables.id
            ? {
                ...comment,
                hasReported,
              }
            : comment
        ),
      })),
    };
  });

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables.id
              ? {
                  ...comment,
                  hasReported,
                }
              : comment
          ),
        })),
      };
    }
  );

  // comment podcast detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

  queryClient.setQueriesData<ICommentResponse>({ queryKey: queryKeys.comments.item() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.id || variables.isEpisode) return oldData;

    return {
      ...oldData,
      hasReported,
    };
  });

  // comment episode detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode() });

  queryClient.setQueriesData<ICommentEpisodeResponse>({ queryKey: queryKeys.comments.itemEpisode() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.id || !variables.isEpisode) return oldData;

    return {
      ...oldData,
      hasReported,
    };
  });

  // remove post account liked
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.getUserLikesPostsHistoryInfinite() });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.userProfile.getUserLikesPostsHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => comment.id !== variables.id),
        })),
      };
    }
  );

  // remove from community feed
  await queryClient.cancelQueries({ queryKey: queryKeys.podcasts.communityFeed() });

  queryClient.setQueriesData<InfiniteData<IGetCommunityFeedResponse>>(
    { queryKey: queryKeys.podcasts.communityFeed() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((comment) => comment.commentId !== variables.id),
        })),
      };
    }
  );
};

export const reportReplyCommentCache = async (
  queryClient: QueryClient,
  variables: { id: string; isEpisode?: boolean; isReport?: boolean }
) => {
  const hasReported = variables?.isReport !== undefined ? variables?.isReport : true;

  // infinite comment reply
  await queryClient.cancelQueries({ queryKey: queryKeys.commentsReply.list() });

  queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
    { queryKey: queryKeys.commentsReply.list() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables.id
              ? {
                  ...comment,
                  hasReported,
                }
              : comment
          ),
        })),
      };
    }
  );

  // infinite comment reply episode
  await queryClient.cancelQueries({ queryKey: queryKeys.commentsReply.listEpisode() });

  queryClient.setQueriesData<InfiniteData<IGetReplyUserHistoryResponse>>(
    { queryKey: queryKeys.commentsReply.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page?.data?.map((comment) =>
            comment.id === variables.id
              ? {
                  ...comment,
                  hasReported,
                }
              : comment
          ),
        })),
      };
    }
  );
};
