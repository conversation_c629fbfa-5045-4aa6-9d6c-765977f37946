import queryKeys from '@/utils/queryKeys';
import { InfiniteData, QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { IGetListBlockedUserResponse } from '../user';

export const handleToggleFollowUser = (
  queryClient: QueryClient,
  error: AxiosError<unknown, any> | null,
  userId: string | number
) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followersInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followingInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followersList() });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followingList() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId() });

  if (error) {
    queryClient.invalidateQueries({ queryKey: queryKeys.referral.friends() });
  }
};

export const blockedUserCache = async (queryClient: QueryClient, variables: { userId: string }) => {
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.getListBlockedUserInfinite() });

  const snapshotBlockedUser = queryClient.getQueriesData<InfiniteData<IGetListBlockedUserResponse>>({
    queryKey: queryKeys.userProfile.getListBlockedUserInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetListBlockedUserResponse>>(
    { queryKey: queryKeys.userProfile.getListBlockedUserInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData?.pages?.map((page) => ({
          ...page,
          data: page.data.filter((user) => user.id !== variables.userId),
        })),
      };
    }
  );

  return { snapshotBlockedUser };
};

export const userFeedbackCache = async (queryClient: QueryClient) => {
  queryClient.setQueriesData(
    {
      queryKey: queryKeys.auth.profile(),
    },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        isFeedbacked: true,
        hasFirstLikeOrComment: true,
      };
    }
  );
};
