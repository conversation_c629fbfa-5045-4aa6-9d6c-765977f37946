import { createSelectorFunctions } from 'auto-zustand-selectors-hook';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { zustandStorage } from './storage';

export interface IUserStore {
  accessToken: string;
  refreshToken: string;
  alertProfileRestricted: boolean;
  isShowedWarningRestricted: boolean;
  isShowSetupAddressPatronModal: boolean;
  setIsShowWarningRestricted: (isShowed: boolean) => void;
  setAccessToken: (token: string) => void;
  setRefreshToken: (token: string) => void;
  signOut: () => void;
  setAlertProfileRestricted: (isAlert: boolean) => void;
  setIsShowSetupAddressPatronModal: (isShowed: boolean) => void;
  hasShownCancelSubscriptionToast: boolean;
  setHasShownCancelSubscriptionToast: (isShow: boolean) => void;

  isShowedFeedbackAppModal: boolean;
  setIsShowedFeedbackAppModal: (isShowed: boolean) => void;
}

const useBaseUserStore = create<IUserStore>()(
  persist(
    (set) => ({
      accessToken: '',
      setAccessToken: (token) => set((state) => ({ ...state, accessToken: token })),
      refreshToken: '',
      setRefreshToken: (token) => set((state) => ({ ...state, refreshToken: token })),
      signOut: () => set(() => ({ accessToken: '', refreshToken: '' })),
      alertProfileRestricted: true,
      setAlertProfileRestricted: (isAlert) => set((state) => ({ ...state, alertProfileRestricted: isAlert })),
      isShowedWarningRestricted: false,
      setIsShowWarningRestricted: (isShowed) => set((state) => ({ ...state, isShowedWarningRestricted: isShowed })),
      isShowSetupAddressPatronModal: false,
      setIsShowSetupAddressPatronModal: (isShow) =>
        set((state) => ({ ...state, isShowSetupAddressPatronModal: isShow })),
      hasShownCancelSubscriptionToast: false,
      setHasShownCancelSubscriptionToast: (isShow) => {
        set((state) => ({ ...state, hasShownCancelSubscriptionToast: isShow }));
      },
      isShowedFeedbackAppModal: false,
      setIsShowedFeedbackAppModal: (isShowed) => set((state) => ({ ...state, isShowedFeedbackAppModal: isShowed })),
    }),
    {
      name: 'user-store',
      storage: createJSONStorage(() => zustandStorage),
    }
  )
);

export const useUserStore = createSelectorFunctions(useBaseUserStore);
