import { useGetProfileQuery } from '@/apis/auth/queries';
import { IconLoading } from '@/components/IconLoading';
import { SendFeedbackAppModal } from '@/components/SendFeedbackAppModal';
import { SetupAddressPatronModal } from '@/components/SetupAddressPatronModal';
import { WarningRestrictModal } from '@/components/WarningRestrictModal';
import ChoosePlanProvider from '@/modules/choose-plan/ChoosePlanProvider';
import { useCommonStore } from '@/store/common';
import { useUserStore } from '@/store/user';
import { Stack } from 'expo-router';
import { View } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';

export default function AppLayout() {
  const { data: userProfile } = useGetProfileQuery();
  const isShowedWarningRestricted = useUserStore.use.isShowedWarningRestricted();
  const isShowSetupAddressPatronModal = useUserStore.use.isShowSetupAddressPatronModal();
  const isShowedFeedbackAppModal = useUserStore.use.isShowedFeedbackAppModal();
  const isBackdropScreenShow = useCommonStore.use.isBackdropScreenShow();

  const isVisibleRestrict = userProfile ? isShowedWarningRestricted : false;

  const backdropOpacity = useAnimatedStyle(() => {
    if (!isBackdropScreenShow)
      return {
        backgroundColor: withTiming('rgba(0, 0, 0, 0.7)', { duration: 500 }),
        zIndex: -1,
        opacity: 0,
      };

    return {
      backgroundColor: withTiming('rgba(0, 0, 0, 0.7)', { duration: 500 }),
      zIndex: 9999,
      opacity: 1,
    };
  });

  return (
    <ChoosePlanProvider>
      <View style={{ flex: 1 }}>
        <Stack screenOptions={{ contentStyle: { backgroundColor: 'transparent' } }}>
          <Stack.Screen name='(tabs)' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/index' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-likes' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-upvote' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-reviews' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-watchlist' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-watched' options={{ headerShown: false }} />

          <Stack.Screen name='categories-all' options={{ headerShown: false }} />

          {/* <Stack.Screen name='show' options={{ headerShown: false }} /> */}

          <Stack.Screen name='podcast' options={{ headerShown: false }} />

          <Stack.Screen name='episode' options={{ headerShown: false }} />

          <Stack.Screen name='category/[id]' options={{ headerShown: false }} />

          <Stack.Screen name='discover-search' options={{ headerShown: false, animation: 'none' }} />

          {/* <Stack.Screen name='review/[id]' options={{ headerShown: false }} /> */}

          <Stack.Screen name='profile-followers' options={{ headerShown: false }} />

          <Stack.Screen name='favorite-shows' options={{ headerShown: false }} />

          <Stack.Screen name='favorite-episodes' options={{ headerShown: false }} />

          <Stack.Screen name='account-update' options={{ headerShown: false }} />

          <Stack.Screen name='account-blocked-user' options={{ headerShown: false }} />

          <Stack.Screen name='settings' options={{ headerShown: false }} />

          <Stack.Screen name='spread-vibes' options={{ headerShown: false }} />

          <Stack.Screen name='subscription-plan' options={{ headerShown: false }} />

          <Stack.Screen name='edit-account' options={{ headerShown: false }} />

          <Stack.Screen name='create-password' options={{ headerShown: false }} />

          <Stack.Screen name='change-password' options={{ headerShown: false }} />

          <Stack.Screen name='verify-code-create-password' options={{ headerShown: false }} />

          <Stack.Screen name='verify-code' options={{ headerShown: false }} />

          <Stack.Screen name='choose-plan' options={{ headerShown: false }} />

          <Stack.Screen name='update-info' options={{ headerShown: false }} />

          <Stack.Screen name='choose-interest' options={{ headerShown: false }} />

          <Stack.Screen name='choose-podcast' options={{ headerShown: false }} />

          <Stack.Screen name='set-address' options={{ headerShown: false }} />
        </Stack>

        <WarningRestrictModal isVisible={isVisibleRestrict} />
        <SetupAddressPatronModal isVisible={isShowSetupAddressPatronModal} />
        <SendFeedbackAppModal isVisible={isShowedFeedbackAppModal} />
      </View>

      <Animated.View
        style={[
          backdropOpacity,
          {
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          },
        ]}
      >
        <IconLoading />
      </Animated.View>
    </ChoosePlanProvider>
  );
}
