import { useColorScheme } from '@/hooks/useColorScheme.web';
import queryKeys from '@/utils/queryKeys';
import { toastConfig } from '@/utils/toast';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PropsWithChildren } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { NotifierWrapper } from 'react-native-notifier';
import { MenuProvider } from 'react-native-popup-menu';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import ToastManager from 'toastify-react-native';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';

const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError(error: any) {
        if (error?.code === 403) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.auth.profile(),
          });
        }
      },
    },
  },
});

const Providers = ({ children }: PropsWithChildren) => {
  const colorScheme = useColorScheme();

  return (
    <KeyboardProvider>
      <SafeAreaProvider>
        <GestureHandlerRootView>
          <NotifierWrapper>
            <QueryClientProvider client={queryClient}>
              <ActionSheetProvider>
                <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
                  <MenuProvider>{children}</MenuProvider>

                  <ToastManager config={toastConfig} />
                </ThemeProvider>
              </ActionSheetProvider>
            </QueryClientProvider>
          </NotifierWrapper>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </KeyboardProvider>
  );
};

export default Providers;
