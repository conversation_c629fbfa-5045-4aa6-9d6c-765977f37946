/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const Colors = {
  light: {
    text: '#11181C',
    background: '#0E100F',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,

    primary: '#D9FF03',
    primaryOpacity10: '#D9FF031A',
    secondary: '#0A0244',
    stateInfo: '#4285F4',
    stateSuccess: '#1EAE5B',
    stateWarning: '#FFB200',
    stateError: '#FC2824',
    stateErrorOpacity20: '#FC282433',
    neutralWhite: '#FFFFFF',
    neutralLightGrey: '#959695',
    neutralGrey: '#434343',
    neutralDarkGrey: '#202020',
    neutralCard: '#1F1E1E',
    neutralBackground: '#0E100F',

    textSecondary: '#A9B0C2',

    onboardingGradient1: ['#0E100F', '#353837'] as [string, string],
    onboardingGradient2: ['#0E100F', '#E6E0D0'] as [string, string],
    onboardingGradient3: ['rgba(0,0,0,1)', 'rgba(0,0,0,0)'] as [string, string],
    foundingPatronGradient: ['#0E100F', '#D9FF0366'] as [string, string],

    whiteOpacity10: 'rgba(255, 255, 255, 0.1)',
    whiteOpacity16: 'rgba(255, 255, 255, 0.16)',
    whiteOpacity20: 'rgba(255, 255, 255, 0.2)',
    whiteOpacity24: 'rgba(255, 255, 255, 0.24)',
    whiteOpacity40: 'rgba(255, 255, 255, 0.4)',
    whiteOpacity56: 'rgba(255, 255, 255, 0.56)',
    whiteOpacity64: 'rgba(255, 255, 255, 0.64)',
    whiteOpacity80: 'rgba(255, 255, 255, 0.8)',
    whiteOpacity4: '#FFFFFF0A',

    spotify: '#1ED760',
    facebook: '#0866FF',

    discoverHeaderGradient: ['#0E100F', '#D9FF030D'] as [string, string],
  },
  dark: {
    text: '#ECEDEE',
    background: '#0E100F',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
};
