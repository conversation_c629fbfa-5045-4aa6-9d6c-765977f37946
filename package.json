{"name": "rabid-app", "main": "index.ts", "version": "1.1.0", "scripts": {"patch": "patch-package", "start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "format": "biome format .", "format:fix": "biome format .  --write", "postinstall": "husky", "prepare": "husky", "prebuild": "pnpm expo prebuild", "prebuildClean": "pnpm expo prebuild --clean", "build-android": "eas build -p android --profile production --local", "build-apk": "cd android && ./gradlew app:assembleRelease && cd ..", "build-bundle": "cd android && ./gradlew bundleRelease && cd .."}, "jest": {"preset": "jest-expo"}, "dependencies": {"@babel/runtime": "^7.27.1", "@expo-google-fonts/inter": "^0.4.1", "@expo/react-native-action-sheet": "^4.1.1", "@expo/vector-icons": "14.1.0", "@gorhom/bottom-sheet": "^5.1.4", "@hookform/resolvers": "^5.0.1", "@invertase/react-native-apple-authentication": "^2.4.1", "@legendapp/list": "^1.0.14", "@likashefqet/react-native-image-zoom": "^4.3.0", "@mantine/hooks": "^8.0.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-native/gradle-plugin": "^0.79.2", "@react-native/metro-config": "^0.79.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/elements": "^2.4.1", "@react-navigation/material-top-tabs": "^7.2.15", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "2.0.0-rc.8", "@tanstack/react-query": "^5.74.7", "arktype": "^2.1.20", "auto-zustand-selectors-hook": "^3.0.1", "axios": "^1.9.0", "babel-preset-expo": "~13.0.0", "date-fns": "^4.1.0", "expo": "~53.0.11", "expo-apple-authentication": "~7.2.4", "expo-asset": "~11.1.5", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.8", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.0", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-media-library": "~17.1.7", "expo-modules-autolinking": "^2.1.11", "expo-router": "~5.0.7", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-updates": "~0.28.14", "expo-web-browser": "~14.1.6", "js-big-decimal": "^2.2.0", "moti": "^0.30.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.2", "react-native": "0.79.3", "react-native-advanced-checkbox": "^2.0.5", "react-native-awesome-gallery": "^0.4.3", "react-native-collapsible-tab-view": "^8.0.1", "react-native-confirmation-code-field": "^7.4.0", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.4", "react-native-draggable-flatlist": "^4.0.2", "react-native-edge-to-edge": "^1.6.0", "react-native-fbsdk-next": "^13.4.1", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-controller": "^1.17.2", "react-native-mmkv": "^3.2.0", "react-native-modal": "13.0.1", "react-native-notifier": "^2.0.0", "react-native-pager-view": "6.8.1", "react-native-popup-menu": "^0.17.0", "react-native-purchases": "^8.11.3", "react-native-purchases-ui": "^8.11.3", "react-native-ratings": "^8.1.0", "react-native-reanimated": "~3.17.5", "react-native-reorderable-list": "^0.16.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-star-rating-widget": "^1.9.2", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.0", "react-native-unistyles": "2.32.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "toastify-react-native": "^7.2.0", "uuid": "^11.1.0", "zod": "^3.24.4", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@biomejs/biome": "1.9.4", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.14", "@types/react-test-renderer": "^18.3.0", "eas-build-cache-provider": "^16.4.2", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~53.0.7", "patch-package": "^8.0.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "expo": {"autolinking": {"android": {"buildFromSource": [".*"]}}}, "private": true, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}