import { IPodcastSearchInfinite, useGetPodcastsSearchInfiniteQuery } from '@/apis/podcast';
import { SearchInput } from '@/components/SearchInput';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useDebouncedValue } from '@mantine/hooks';
import { router, useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { TouchableOpacity, View, ViewabilityConfig } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PodcastSearchItem } from './components/PodcastSearchItem';
import { PodcastSearchItemSkeleton } from './components/PodcastSearchItemSkeleton';
import { SearchRecent } from './components/SearchRecent';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import 'react-native-get-random-values';
import { v4 as uuid } from 'uuid';

export type SearchAction = 'discover' | 'create-post' | 'add-shows' | 'add-episodes';

export const DiscoverSearch = () => {
  const { searchAction = 'discover' } = useLocalSearchParams<{ searchAction: SearchAction }>();
  const [listKey, setListKey] = useState(uuid());
  const { styles } = useStyles(stylesheet);

  const [searchValue, setSearchValue] = useState('');

  const [searchDebounced] = useDebouncedValue(searchValue, 200);

  const { data, hasNextPage, isFetchingNextPage, fetchNextPage, isPending } = useGetPodcastsSearchInfiniteQuery({
    limit: 40,
    page: 1,
    search: searchDebounced,
    type: searchAction === 'add-shows' ? 'podcast' : searchAction === 'add-episodes' ? 'episode' : undefined,
  });

  const pages = data?.pages ?? [];
  const podcasts = pages?.map((page) => page.data).flat() ?? [];

  const handleCancel = () => {
    router.dismiss();
  };

  const renderItem = useCallback<ListRenderItem<IPodcastSearchInfinite>>(
    ({ item }) => <PodcastSearchItem searchItem={item} searchAction={searchAction} />,
    [searchAction]
  );

  const keyExtractor = useCallback((item: IPodcastSearchInfinite) => `${item.id}-${item.type}`, []);

  const onEndReached = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderSkeleton = useCallback(() => {
    return (
      <>
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <PodcastSearchItemSkeleton key={index} />
          ))}
      </>
    );
  }, []);

  const renderSpacer = useCallback(() => <Spacer height={24} />, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setListKey(uuid());
  }, [searchDebounced]);

  const placeholder =
    searchAction === 'add-shows'
      ? 'Search Show'
      : searchAction === 'add-episodes'
        ? 'Search Episode'
        : 'Search Show or Episode';

  const viewabilityConfig = useRef<ViewabilityConfig>({
    waitForInteraction: false,
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 0,
  }).current;

  return (
    <View style={styles.container}>
      <View style={[styles.box, styles.searchBox]}>
        <View style={{ flex: 1 }}>
          <SearchInput placeholder={placeholder} value={searchValue} onChangeText={setSearchValue} />
        </View>

        <TouchableOpacity onPress={handleCancel}>
          <ThemedText style={styles.cancelText}>Cancel</ThemedText>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView behavior='padding' style={styles.full}>
        <View style={[styles.searchContent]}>
          <Spacer height={24} />

          <Show when={!searchValue}>
            <SearchRecent searchAction={searchAction} />
          </Show>

          <Show when={!!searchValue}>
            <FlashList
              key={listKey}
              bounces={false}
              data={podcasts}
              extraData={podcasts}
              showsVerticalScrollIndicator={false}
              // style={{ flex: 1 }}
              // keyboardShouldPersistTaps='handled'
              scrollEventThrottle={16}
              onEndReachedThreshold={0.4}
              onEndReached={onEndReached}
              contentContainerStyle={styles.listContainer}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              ListFooterComponent={isPending ? renderSkeleton : null}
              ListFooterComponentStyle={styles.skeletonContainer}
              ItemSeparatorComponent={renderSpacer}
              estimatedItemSize={64}
            />
          </Show>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom,
    paddingTop: rt.insets.top,
  },
  box: {
    paddingHorizontal: 24,
  },
  searchBox: {
    backgroundColor: theme.colors.neutralCard,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  cancelText: {
    color: theme.colors.neutralLightGrey,
  },
  searchContent: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  skeletonContainer: {
    gap: 24,
  },
  full: {
    flex: 1,
  },
}));
