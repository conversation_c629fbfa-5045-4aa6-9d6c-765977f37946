import { Header } from '@/components/ui/Header';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikesEpisodes } from './components/tabs/LikesEpisodes';
import { LikesShow } from './components/tabs/LikesShow';
import { useLocalSearchParams } from 'expo-router';
import { LikesPost } from './components/tabs/LikesPost';
import { useIsYou } from '@/hooks/useIsYou';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';
import { truncateHeader } from '@/utils/func';

export const AccountLikes = () => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  const title = isPendingUserProfile ? '' : isYou ? 'My Likes' : `${userProfileById?.username} Likes`;
  const titleTruncated = truncateHeader(title || '');

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header title={titleTruncated} isBack />
      </View>

      <TabContainer containerStyle={styles.tabContainer}>
        <TabWrapper tabName='Show' label={'Show'}>
          <LikesShow userId={userId} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName='Episodes' label={'Episodes'}>
          <LikesEpisodes userId={userId} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName='Post' label={'Post'}>
          <LikesPost userId={userId} profile={userProfileById} />
        </TabWrapper>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
}));
