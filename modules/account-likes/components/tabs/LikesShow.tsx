import { IUserProfileById, UserLikesPodcast } from '@/apis/user';
import { useGetInfiniteUserLikesPodcastsHistoryQuery } from '@/apis/user/queries';
import { Icons } from '@/assets/icons';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { ThemedText } from '@/components/ThemedText';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { showDetailDirect } from '@/utils/router-prefetch';
import { ListRenderItem } from '@shopify/flash-list';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikeShowItem } from '../LikeShowItem';
import { Spacer } from '@/components/Spacer';
import queryKeys from '@/utils/queryKeys';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

export const LikesShow = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const [listType, setListType] = useState<'list' | 'grid'>('grid');

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: likesPodcastsData,
    isPending,
    isSuccess,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteUserLikesPodcastsHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesPodcastsPages = likesPodcastsData?.pages ?? [];
  const likesPodcasts = likesPodcastsPages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    (item: UserLikesPodcast) => {
      showDetailDirect(queryClient, item.podcastId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserLikesPodcast>>(
    ({ item }) => <LikeShowItem item={item} listType={listType} itemSize={itemSize} onPress={handleDirect} />,
    [handleDirect, listType]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: UserLikesPodcast) => `${item.id}_${item.podcastId}`, []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerList}>
        <ThemedText type='defaultSemiBold'>Recent</ThemedText>

        {listType === 'list' ? (
          <TouchableOpacity onPress={() => setListType('grid')} activeOpacity={0.7}>
            <Icons.GridIcon size={24} color='#fff' />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={() => setListType('list')} activeOpacity={0.7}>
            <Icons.ListIcon size={24} color='#fff' />
          </TouchableOpacity>
        )}
      </View>
    ),
    [styles, listType]
  );

  const renderSeparator = useCallback(() => <Spacer height={listType === 'list' ? 24 : 28} />, [listType]);

  const handleRefetch = useCallback(async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.userProfile.getUserLikesPodcastsHistoryInfinite() });
  }, [queryClient]);

  const isShowEmpty = isSuccess && likesPodcasts.length === 0;

  return (
    <TabFlashList
      key={listType}
      extraData={listType}
      keyExtractor={keyExtractor}
      bounces={false}
      numColumns={listType === 'list' ? 1 : 3}
      data={likesPodcasts}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      onRefresh={handleRefetch}
      refreshing={isPending}
      ListHeaderComponent={renderHeader}
      renderItem={renderItem}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      estimatedItemSize={listType === 'list' ? 64 : itemSize}
      ItemSeparatorComponent={renderSeparator}
      ListFooterComponent={isFetchingNextPage ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <View style={styles.emptyContainer}>
            <Empty
              type='like'
              emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any shows`}
            />
          </View>
        ) : null
      }
    />
  );
});

const HEADER_HEIGHT = 48;
const LIST_LAYOUT_HEIGHT = 64;

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  emptyContainer: {
    flexGrow: 1,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    paddingHorizontal: 24,
    minHeight: rt.screen.height - rt.insets.top - rt.insets.bottom - LIST_LAYOUT_HEIGHT - HEADER_HEIGHT,
  },
  headerList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
  },
}));
