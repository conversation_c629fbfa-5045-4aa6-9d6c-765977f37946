import { useCallback, useState } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useLocalSearchParams } from 'expo-router';

import { Header } from '@/components/ui/Header';
import { CustomTabBarWithSearch, FollowersTab, FollowingTab } from './components';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useGetFollowersQuery, useGetFollowingQuery } from '@/apis/follow';
import { checkIsOwner, getUserIdForFavorites, formatCompactNumber, truncateHeader } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { useToggleFollowUserMutation } from '@/apis/user';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';
import { TabBarProps } from '@/components/collapsing-tabs/TabNavigator';

export const ProfileFollowersScreen = () => {
  const { styles } = useStyles(stylesheet);
  const [searchQuery, setSearchQuery] = useState('');

  const {
    userId: routerUserId,
    username: routerUsername,
    initialTab,
  } = useLocalSearchParams<{
    userId?: string;
    username?: string;
    initialTab?: string;
  }>();
  const { data: userProfile } = useGetProfileQuery();

  const targetUserId = getUserIdForFavorites(routerUserId, userProfile?.id);
  const isOwner = checkIsOwner(userProfile?.id, targetUserId);
  const { data: followersData, refetch: refetchFollowers } = useGetFollowersQuery({
    targetUserId: Number(targetUserId),
    limit: 1,
  });
  const { data: followingData, refetch: refetchFollowing } = useGetFollowingQuery({
    targetUserId: Number(targetUserId),
    limit: 1,
  });
  const toggleFollowMutation = useToggleFollowUserMutation();

  const handleFollowToggle = (userId: string, isFollowed: boolean) => {
    toggleFollowMutation.mutate(Number(userId), {
      onSuccess: (data) => {
        toastSuccess({
          description: !isFollowed ? 'Followed this user successfully' : 'Unfollowed this user successfully',
        });
        refetchFollowers();
        refetchFollowing();
      },
      onError: (error) => {
        toastError(!isFollowed ? 'Followed this user unsuccessfully' : 'Unfollowed this user unsuccessfully');
      },
    });
  };

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const renderCustomTabBar = useCallback(
    (tabBarProps: TabBarProps) => (
      <CustomTabBarWithSearch {...tabBarProps} searchQuery={searchQuery} onSearchChange={handleSearchChange} />
    ),
    [searchQuery, handleSearchChange]
  );

  const displayUserName = routerUsername || userProfile?.username || 'Profile';
  const initialTabIndex = Number.isInteger(Number(initialTab)) ? Number(initialTab) : 0;
  const totalFollowers = followersData?.pagination.totalItems || 0;
  const totalFollowing = followingData?.pagination.totalItems || 0;

  const titleTruncated = truncateHeader(displayUserName || '');

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Header isBack title={titleTruncated} titleStyle={styles.headerTitle} />
      </View>

      <TabContainer
        containerStyle={styles.tabContainer}
        headerContainerStyle={styles.headerContainer}
        renderCustomTabBar={renderCustomTabBar}
        initialTabIndex={initialTabIndex}
      >
        <TabWrapper
          tabName='followers'
          label={`${formatCompactNumber(totalFollowers)} ${totalFollowers <= 1 ? 'Follower' : 'Followers'}`}
        >
          <FollowersTab
            onFollowToggle={handleFollowToggle}
            searchQuery={searchQuery}
            targetUserId={targetUserId}
            isOwner={isOwner}
            refetchFollowers={refetchFollowers}
            displayUserName={displayUserName}
            totalFollowers={totalFollowers}
          />
        </TabWrapper>

        <TabWrapper tabName='following' label={`${formatCompactNumber(totalFollowing)} Following`}>
          <FollowingTab
            onFollowToggle={handleFollowToggle}
            searchQuery={searchQuery}
            targetUserId={targetUserId}
            isOwner={isOwner}
            refetchFollowing={refetchFollowing}
            displayUserName={displayUserName}
            totalFollowing={totalFollowing}
          />
        </TabWrapper>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rt.insets.top,
  },
  headerTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 20,
    lineHeight: 32,
    ...theme.fw600,
  },
  headerContainer: {
    paddingHorizontal: 24,
    backgroundColor: theme.colors.background,
  },
  tabContainer: {
    overflow: 'hidden',
  },
}));
