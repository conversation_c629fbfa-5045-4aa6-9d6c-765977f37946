import { useCallback, useMemo, useState } from 'react';
import { ListRenderItem } from '@shopify/flash-list';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { UserListItem } from '@/components/ui/UserListItem';
import { Spacer } from '@/components/Spacer';
import { IconLoading } from '@/components/IconLoading';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { useGetFollowersInfiniteQuery, type IFollow } from '@/apis/follow';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import 'react-native-get-random-values';
import { v4 as uuid } from 'uuid';
import { Show } from '@/components/Show';
import { Empty } from '@/components/Empty';
import { useDebouncedValue } from '@mantine/hooks';
import { View } from 'react-native';

interface FollowersTabProps {
  onFollowToggle: (userId: string, isFollowed: boolean) => void;
  searchQuery: string;
  targetUserId?: string;
  isOwner: boolean;
  refetchFollowers: () => void;
  displayUserName: string;
  totalFollowers: number;
}

export const FollowersTab = ({
  onFollowToggle,
  searchQuery,
  targetUserId,
  isOwner,
  refetchFollowers,
  displayUserName,
  totalFollowers,
}: FollowersTabProps) => {
  const { styles } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const [listKey, setListKey] = useState(uuid());

  const [debouncedSearchQuery] = useDebouncedValue(searchQuery, 500);

  const {
    data: followersData,
    isSuccess,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useGetFollowersInfiniteQuery({
    targetUserId: Number(targetUserId),
    limit: 20,
    search: debouncedSearchQuery,
  });

  const allFollowers = useMemo(() => {
    const infiniteData = followersData as any;
    if (!infiniteData?.pages) return [];

    return infiniteData.pages.flatMap((page: any) => page.data || []);
  }, [followersData]);

  const handleFollowToggle = useCallback(
    (userId: string, isFollowed: boolean) => {
      onFollowToggle(userId, isFollowed);
    },
    [onFollowToggle]
  );

  const renderUserItem = useCallback<ListRenderItem<IFollow>>(
    ({ item }) => {
      return (
        <UserListItem
          user={item}
          onUserPress={() => {
            const isRestricted = onCheckAccountRestricted();
            if (isRestricted) return;

            router.push({
              pathname: '/(app)/[userId]',
              params: {
                userId: item?.id?.toString(),
              },
            });
          }}
          onFollowToggle={handleFollowToggle}
          showFollowButton={item.id !== userProfile?.id}
        />
      );
    },
    [handleFollowToggle, onCheckAccountRestricted, userProfile?.id]
  );

  const keyExtractor = useCallback((item: IFollow) => item.id?.toString() || '', []);

  const renderSeparator = useCallback(() => <Spacer height={24} />, []);

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleRefresh = useCallback(async () => {
    await Promise.all([refetchFollowers(), refetch()]);
  }, [refetch, refetchFollowers]);

  const isShowEmpty = isSuccess && totalFollowers === 0;

  return (
    <FlashListAnimate
      data={allFollowers}
      renderItem={renderUserItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContainer}
      ItemSeparatorComponent={renderSeparator}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      onRefresh={handleRefresh}
      ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
      flashListKey={listKey}
      ListEmptyComponent={
        <Show when={isShowEmpty}>
          <View style={styles.emptyContainer}>
            <Empty
              type='follow'
              emptyText={`${isOwner ? 'You' : displayUserName} ${isOwner ? `haven't` : `hasn't`} got any followers yet.`}
            />
          </View>
        </Show>
      }
    />
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: rt.insets.bottom + 24,
    flexGrow: 1,
  },
  emptyContainer: {
    flexGrow: 1,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
