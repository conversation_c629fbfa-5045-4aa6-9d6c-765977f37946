import { useCallback, useMemo, useState } from 'react';
import { ListRenderItem } from '@shopify/flash-list';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { UserListItem } from '@/components/ui/UserListItem';
import { Spacer } from '@/components/Spacer';
import { IconLoading } from '@/components/IconLoading';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { useGetFollowingInfiniteQuery, type IFollow } from '@/apis/follow';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { Show } from '@/components/Show';
import { Empty } from '@/components/Empty';
import 'react-native-get-random-values';
import { v4 as uuid } from 'uuid';
import { useDebouncedValue } from '@mantine/hooks';
import { View } from 'react-native';

interface FollowingTabProps {
  onFollowToggle: (userId: string, isFollowed: boolean) => void;
  searchQuery: string;
  targetUserId?: string;
  isOwner: boolean;
  refetchFollowing: () => void;
  displayUserName: string;
  totalFollowing: number;
}

export const FollowingTab = ({
  onFollowToggle,
  searchQuery,
  targetUserId,
  isOwner,
  refetchFollowing,
  totalFollowing,
  displayUserName,
}: FollowingTabProps) => {
  const { styles } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const [listKey, setListKey] = useState(uuid());

  const [debouncedSearchQuery] = useDebouncedValue(searchQuery, 500);

  const {
    data: followingData,
    isSuccess,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useGetFollowingInfiniteQuery({
    targetUserId: Number(targetUserId),
    limit: 20,
    search: debouncedSearchQuery,
  });

  const allFollowing = useMemo(() => {
    const infiniteData = followingData as any;
    if (!infiniteData?.pages) return [];

    return infiniteData.pages.flatMap((page: any) => page.data || []);
  }, [followingData]);

  const handleFollowToggle = useCallback(
    (userId: string, isFollowed: boolean) => {
      onFollowToggle(userId, isFollowed);
    },
    [onFollowToggle]
  );

  const renderUserItem = useCallback<ListRenderItem<IFollow>>(
    ({ item }) => {
      return (
        <UserListItem
          user={item}
          onUserPress={() =>
            router.push({
              pathname: '/(app)/[userId]',
              params: {
                userId: item?.id?.toString(),
              },
            })
          }
          onFollowToggle={handleFollowToggle}
          showFollowButton={item.id !== userProfile?.id}
        />
      );
    },
    [handleFollowToggle, userProfile?.id]
  );

  const keyExtractor = useCallback((item: IFollow) => item.id?.toString() || '', []);

  const renderSeparator = useCallback(() => <Spacer height={24} />, []);

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleRefresh = useCallback(async () => {
    await Promise.all([refetchFollowing(), refetch()]);
  }, [refetch, refetchFollowing]);

  const isShowEmpty = isSuccess && totalFollowing === 0;

  return (
    <FlashListAnimate
      data={allFollowing}
      renderItem={renderUserItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContainer}
      ItemSeparatorComponent={renderSeparator}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      onRefresh={handleRefresh}
      ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
      flashListKey={listKey}
      ListEmptyComponent={
        <Show when={isShowEmpty}>
          <View style={styles.emptyContainer}>
            <Empty
              type='follow'
              emptyText={`${isOwner ? 'You' : displayUserName} ${isOwner ? `haven't` : `hasn't`} followed anyone yet.`}
            />
          </View>
        </Show>
      }
    />
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: rt.insets.bottom,
    flexGrow: 1,
  },
  emptyContainer: {
    flexGrow: 1,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
