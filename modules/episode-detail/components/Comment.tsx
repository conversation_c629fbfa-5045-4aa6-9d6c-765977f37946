import {
  useDeleteEpisodeCommentMutation,
  useLikeEpisodeCommentMutation,
  useReportEpisodeCommentMutation,
  useVoteEpisodeCommentMutation,
} from '@/apis/comment/mutations';
import { ICommentCommunity } from '@/apis/comment/types';
import { IEpisode } from '@/apis/podcast';
import { Icons } from '@/assets/icons';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { toastError, toastSuccess } from '@/utils/toast';
import { router } from 'expo-router';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import DeleteCommentModal from './DeleteCommentModal';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { UserProfileText } from '@/components/UserProfileText';
import { useIsYou } from '@/hooks/useIsYou';
import CommentImage from '@/modules/show-detail/components/CommentImage';
import Tag from '@/components/Tag';
import CommentActions from '@/modules/show-detail/components/CommentAction';
import CommentMenu from '@/modules/show-detail/components/CommentMenu';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { useQueryClient } from '@tanstack/react-query';
import { reportCommentCache } from '@/apis/settled-handler/comment';
import { ReportModal } from '@/components/ReportModal';

interface CommentProps {
  comment: ICommentCommunity & { page?: number; limit?: number };
  episodeId?: string;
  refetchList?: () => void;
  episode?: IEpisode;
}

const Comment: React.FC<CommentProps> = memo(({ comment, episodeId }) => {
  const { styles, theme } = useStyles(stylesheet);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const isOwner = useIsYou({
    userId: comment.user.id.toString(),
  });
  const queryClient = useQueryClient();

  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [openMenu, setOpenMenu] = React.useState(false);
  const [showDeleteCommentModal, setShowDeleteCommentModal] = useState(false);

  const { mutateAsync: deleteComment, isPending: isPendingDeleteComment } = useDeleteEpisodeCommentMutation();
  const { mutateAsync: likeComment } = useLikeEpisodeCommentMutation();
  const { mutateAsync: voteEpisodeComment } = useVoteEpisodeCommentMutation();
  const { mutateAsync: reportComment, isPending: isReporting } = useReportEpisodeCommentMutation();

  const handlerDeleteComment = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await deleteComment({ id: comment.id });

      toastSuccess({
        description: 'Deleted post successfully',
      });
    } catch (error) {
      toastError(error);
    } finally {
      setShowDeleteCommentModal(false);
    }
  };

  const usernameDisplay = useMemo(() => {
    if (isOwner) return 'You';

    return comment.user.username;
  }, [comment.user.username, isOwner]);

  const handleDirect = () => {
    router.push({
      pathname: '/(app)/episode/[episodeId]/review/[postId]',
      params: {
        episodeId: comment.parentId,
        postId: comment.id,
        source: comment.source,
      },
    });
  };

  const handleLikeComment = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await likeComment({ id: comment.id, source: comment.source });
    } catch (error) {
      toastError(error);
    }
  };

  const handleEditPost = () => {
    setOpenMenu(false);
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push(`/(app)/episode/${episodeId}/edit-post/${comment.id}`);
  };

  const handleConfirmDeletePost = () => {
    setOpenMenu(false);

    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowDeleteCommentModal(true);
  };

  const handleVoteComment = async (vote: 'up' | 'down') => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await voteEpisodeComment({ commentId: comment.id, source: comment.source, vote });
    } catch (error) {
      toastError(error);
    }
  };

  const handleConfirmReport = useCallback(() => {
    setShowConfirmationModal(true);
  }, []);

  const handleCloseConfirmReport = useCallback(() => {
    setShowConfirmationModal(false);
  }, []);

  const handleReport = async (reasons: string[]) => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await reportComment({
        commentId: comment.id,
        source: comment.source,
        reasons,
      });
      handleCloseConfirmReport();
      toastSuccess({ description: 'You Report This Post' });
    } catch (error) {
      toastError(error);
    }
  };

  const handleViewCommentReported = () => {
    reportCommentCache(queryClient, {
      id: comment.id,
      isReport: false,
      isEpisode: false,
    });
  };

  if (comment.hasReported) {
    return (
      <View style={styles.commentReportContainer}>
        <ThemedText type='tinyMedium' style={styles.header}>
          You reported this post.
        </ThemedText>

        <Pressable onPress={handleViewCommentReported} style={({ pressed }) => ({ opacity: pressed ? 0.7 : 1 })}>
          <ThemedText type='tinyMedium'>View</ThemedText>
        </Pressable>
      </View>
    );
  }

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleDirect} style={styles.container}>
      <UserProfileTouch userId={comment.user.id} userType={comment.source} style={styles.userImageContainer}>
        <Avatar image={comment.user.avatar} size={48} />
      </UserProfileTouch>

      <View style={styles.contentContainer}>
        <View style={{ paddingRight: 24 }}>
          <View style={styles.userBox}>
            <View style={{ flex: 1 }}>
              <ThemedText style={styles.header} type='tinyMedium' numberOfLines={2}>
                <UserProfileText userId={comment.user.id} userType={comment.source}>
                  <ThemedText
                    type='tinyMedium'
                    style={[
                      styles.header,
                      isOwner
                        ? {
                            color: theme.colors.primary,
                          }
                        : null,
                    ]}
                  >
                    {usernameDisplay}
                  </ThemedText>
                </UserProfileText>{' '}
                on {comment.parentTitle}
              </ThemedText>
            </View>

            <CommentMenu
              open={openMenu}
              setOpen={setOpenMenu}
              onEditPost={handleEditPost}
              onDeletePost={handleConfirmDeletePost}
              isOwner={isOwner}
              onReportPost={handleConfirmReport}
            >
              <Icons.EllipsisVertical size={24} color={'rgba(255, 255, 255, 0.3)'} />
            </CommentMenu>
          </View>

          <Spacer height={8} />

          <Show when={comment.title?.trim().length > 0}>
            <ThemedText style={styles.title}>{comment.title?.trim()}</ThemedText>
          </Show>

          <Show when={comment.content?.trim().length > 0}>
            <ThemedText type='small' style={styles.commentText}>
              {comment.content}
              {comment.isEdited && (
                <ThemedText type='tinyMedium' style={styles.commentEditedText}>
                  {' '}
                  (Edited)
                </ThemedText>
              )}
            </ThemedText>
          </Show>
        </View>

        <Show when={comment.images.length > 0}>
          <>
            <Spacer height={16} />

            <CommentImage images={comment.images} />
          </>
        </Show>

        <Show when={!!comment.tag}>
          <Spacer height={16} />

          <Tag tagName={comment.tag} size={28} />
        </Show>

        <Spacer height={16} />

        <View style={{ paddingRight: 24 }}>
          <CommentActions comment={comment} onLikePress={handleLikeComment} onVotePress={handleVoteComment} />
        </View>
      </View>

      <Show when={isOwner}>
        <DeleteCommentModal
          visible={showDeleteCommentModal}
          onClose={() => setShowDeleteCommentModal(false)}
          onDelete={handlerDeleteComment}
          isPendingDelete={isPendingDeleteComment}
        />
      </Show>

      <ReportModal
        isVisible={showConfirmationModal}
        onClose={handleCloseConfirmReport}
        onConfirm={handleReport}
        isLoading={isReporting}
      />
    </TouchableOpacity>
  );
});

const stylesheet = createStyleSheet((theme) => ({
  commentReportContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 24,
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
    minHeight: 50,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginRight: 24,
  },
  container: {
    flexDirection: 'row',
  },
  userImageContainer: {
    marginRight: 24,
  },
  userImage: {
    width: 48,
    height: 48,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralGrey,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    ...theme.fw500,
    lineHeight: 18,
    color: theme.colors.whiteOpacity56,
  },
  title: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    marginBottom: 4,
  },
  commentText: {
    color: theme.colors.neutralWhite,
    fontSize: 14,
    lineHeight: 20,
    ...theme.fw500,
  },
  imagesContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    flex: 1,
  },
  commentImage: {
    aspectRatio: 1,
    borderRadius: 8,
  },
  userBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  commentEditedText: {
    color: theme.colors.neutralLightGrey,
  },
}));

export default Comment;
