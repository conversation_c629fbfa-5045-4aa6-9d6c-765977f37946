import { Spacer } from '@/components/Spacer';
import { memo, useMemo } from 'react';
import { View } from 'react-native';
import Animated, { interpolate, interpolateColor, useAnimatedStyle } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountHeader } from './AccountHeader';
import { ProfileHeader } from './ProfileHeader';
import { ProfileInfo } from './ProfileInfo';
import { IUserProfileById } from '@/apis/user';
import { useCollapsingTabsContext } from '@/contexts/app.context';

type Props = {
  isYouTab: boolean;
  isYou: boolean;
  profile: IUserProfileById;
};

export const AccountTabsHeader = memo(({ isYouTab, isYou, profile }: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const insets = useSafeAreaInsets();
  const { refPager, currentScroll: scrollY, headerDiff: height } = useCollapsingTabsContext();

  const headerStyle = useAnimatedStyle(() => {
    const maxScroll = height;

    return {
      transform: [
        {
          translateY: interpolate(scrollY.value, [0, maxScroll], [0, maxScroll], 'clamp'),
        },
      ],
      backgroundColor: interpolateColor(
        scrollY.value,
        [0, insets.top],
        ['transparent', theme.colors.neutralBackground]
      ),
    };
  });

  const isPatron = useMemo(() => {
    if (!profile) return false;
    return profile?.type === 'premium';
  }, [profile]);

  const onChangeTab = (pageIndex: number) => {
    refPager.current?.setPage(pageIndex);
  };

  return (
    <Animated.View style={styles.container} pointerEvents='box-none'>
      <Animated.View style={[styles.headerScreenContainer, headerStyle]} pointerEvents={'box-none'}>
        <AccountHeader isYouTab={isYouTab} isYou={isYou} profile={profile} />
      </Animated.View>

      <View pointerEvents={'box-none'}>
        <ProfileHeader isYouTab={isYouTab} isPatron={isPatron} />
        <ProfileInfo isYouTab={isYouTab} profile={profile} onChangeTab={onChangeTab} />
      </View>

      <Spacer height={24} />
    </Animated.View>
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
  headerScreenContainer: {
    paddingTop: rt.insets.top,
    paddingHorizontal: 24,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 99,
  },
}));
