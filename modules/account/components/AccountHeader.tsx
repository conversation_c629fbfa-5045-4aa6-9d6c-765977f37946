import { IUserProfileById, useBlockUserMutation } from '@/apis/user';
import { Icons } from '@/assets/icons';
import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { useScaleFocus } from '@/hooks/useScaleFocus';
import queryKeys from '@/utils/queryKeys';
import { toastError, toastSuccess } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import { useState } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isYouTab: boolean;
  isYou: boolean;
  profile: IUserProfileById;
};

export const AccountHeader = ({ isYouTab, isYou, profile }: Props) => {
  const { username, id: userId } = profile;
  const { styles } = useStyles(stylesheet);
  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const [visibleProfileMenu, setVisibleProfileMenu] = useState(false);
  const queryClient = useQueryClient();

  const { animatedStyle, handlePressIn, handlePressOut, pressed } = useScaleFocus(0.9);
  const { mutateAsync: blockUser } = useBlockUserMutation();

  const handleEditProfile = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push('/(app)/account-update');
  };

  const handleDirectSetting = () => {
    router.push({
      pathname: '/(app)/settings',
    });
  };

  const isBlockedUser = profile?.isBlocked;

  const handleBlockUser = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      setVisibleProfileMenu(false);
      await blockUser(userId?.toString());
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(userId) });
      toastSuccess({ description: `You ${isBlockedUser ? 'unblocked' : 'blocked'} ${username}` });
    } catch (error) {
      toastError(error);
    }
  };

  const handleMenuPress = () => {
    setVisibleProfileMenu(true);
  };

  return (
    <>
      <Header
        isBack={!isYouTab}
        leftAction={
          <TouchableOpacity onPress={handleDirectSetting}>
            <Icons.Setting size={24} />
          </TouchableOpacity>
        }
        title=''
        rightAction={
          isYouTab ? (
            <CustomButton
              textType={'tinyMedium'}
              type='primaryOpacity10'
              style={styles.editProfile}
              onPress={handleEditProfile}
            >
              Edit Profile
            </CustomButton>
          ) : !isYou ? (
            <Pressable style={styles.menuButton} onPress={handleMenuPress}>
              <Icons.EllipsisVertical size={16} color={'#fff'} />
            </Pressable>
          ) : null
        }
      />

      <BottomModal isVisible={visibleProfileMenu} onClose={() => setVisibleProfileMenu(false)}>
        <View style={styles.modalContentWrapper}>
          <Animated.View style={animatedStyle}>
            <TouchableOpacity
              activeOpacity={0.7}
              style={styles.action}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              onPress={handleBlockUser}
            >
              {isBlockedUser ? <Icons.RemoveCircle size={24} /> : <Icons.CloseCircle size={24} />}

              <ThemedText type='defaultMedium'>{isBlockedUser ? 'Unblock User' : 'Block User'}</ThemedText>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </BottomModal>
    </>
  );
};

const stylesheet = createStyleSheet(() => ({
  editProfile: {
    paddingHorizontal: 16,
    minHeight: 38,
  },
  menuButton: {
    width: 36,
    height: 36,
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF3D',
  },
  modalContentWrapper: {
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  action: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
}));
