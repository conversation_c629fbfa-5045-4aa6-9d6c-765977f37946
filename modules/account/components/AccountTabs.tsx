import { useLocalSearchParams, usePathname } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountTabsHeader } from './AccountTabsHeader';
import { AboutTab } from './tabs/AboutTab';
import { PostTab } from './tabs/PostTab';
import { ReplyTab } from './tabs/ReplyTab';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { useIsYou } from '@/hooks/useIsYou';
import { TabContainer } from '@/components/collapsing-tabs';
import { useCallback, useEffect } from 'react';
import { useIsFocused } from '@react-navigation/native';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';
import { AccountSkeleton } from './AccountSkeleton';
import { ProfileBlocked } from './ProfileBlocked';

type Props = {};

const HEADER_HEIGHT = 58;

export const AccountTabs = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  const insets = useSafeAreaInsets();
  const pathname = usePathname();
  const isYouTab = pathname === '/account';
  const isFocused = useIsFocused();

  const { data: userProfile, refetch: refetchProfile } = useGetProfileQuery({
    refetchOnWindowFocus: true,
  });
  const localSearch = useLocalSearchParams<{ userId: string }>();
  const isYou = useIsYou({
    userId: localSearch?.userId || userProfile?.id?.toString() || '',
  });

  const userId = isYou ? userProfile?.id?.toString() : localSearch?.userId;

  const {
    data: userProfileById,
    isPending: isPendingUserProfile,
    refetch: refetchUserProfileById,
  } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
    refetchOnWindowFocus: true,
  });

  const isBlockedUser = userProfileById?.isBlocked;

  const renderHeader = useCallback(() => {
    if (!userProfileById) return null;

    return <AccountTabsHeader isYou={isYou} isYouTab={isYouTab} profile={userProfileById} />;
  }, [isYouTab, userProfileById, isYou]);

  useEffect(() => {
    if (!isFocused) return;

    if (isYou) {
      refetchProfile();
    }
    refetchUserProfileById();
  }, [isFocused, isYou, refetchProfile, refetchUserProfileById]);

  if (isPendingUserProfile || !userProfileById) return <AccountSkeleton />;

  if (isBlockedUser) {
    return <ProfileBlocked profile={userProfileById} />;
  }

  return (
    <TabContainer
      containerStyle={styles.tabContainer}
      headerContainerStyle={styles.headerContainer}
      initHeaderHeight={508}
      minHeaderHeight={HEADER_HEIGHT + insets.top}
      renderHeader={renderHeader}
    >
      <TabWrapper tabName='About' label={'About'}>
        <AboutTab profile={userProfileById} />
      </TabWrapper>

      <TabWrapper tabName='Post' label={'Post'}>
        <PostTab profile={userProfileById} />
      </TabWrapper>

      <TabWrapper tabName='Reply' label={'Reply'}>
        <ReplyTab profile={userProfileById} />
      </TabWrapper>
    </TabContainer>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
  headerScreenContainer: {
    paddingVertical: 5,
    paddingHorizontal: 24,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    paddingTop: rt.insets.top,
  },
}));
