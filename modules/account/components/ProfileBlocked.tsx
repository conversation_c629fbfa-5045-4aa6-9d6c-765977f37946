import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import queryKeys from '@/utils/queryKeys';
import { toastError, toastSuccess } from '@/utils/toast';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountHeader } from './AccountHeader';
import { IUserProfileById, useBlockUserMutation } from '@/apis/user';
import { ProfileHeader } from './ProfileHeader';
import { UserInfo } from './UserInfo';
import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { useQueryClient } from '@tanstack/react-query';

type Props = {
  profile: IUserProfileById;
};

export const ProfileBlocked = ({ profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const queryClient = useQueryClient();
  const userId = profile?.id;
  const username = profile?.username;
  const { mutateAsync: unblockUser } = useBlockUserMutation();

  const handleUnblockUser = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await unblockUser(userId);
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(userId) });
      toastSuccess({ description: `You unblocked ${username}` });
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <View>
      <View style={styles.headerContainer}>
        <AccountHeader isYouTab={false} isYou={false} profile={profile} />
      </View>

      <View pointerEvents={'box-none'}>
        <ProfileHeader isYouTab={false} isPatron={profile?.type === 'premium'} />

        <UserInfo profile={profile} />
      </View>

      <Spacer height={48} />

      <View style={styles.userIconBox}>
        <Icons.UserBlock size={184} />
      </View>

      <View style={styles.container}>
        <ThemedText type='smallNormal' style={styles.textCenter}>
          You <ThemedText type='smallBold'>blocked</ThemedText> {username}, you won't be able to access anything from
          them. Unblocking will allow you to see their content again.
        </ThemedText>

        <Spacer height={32} />

        <CustomButton type='primary' textType='defaultBold' style={styles.button} onPress={handleUnblockUser}>
          Unblock
        </CustomButton>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingHorizontal: 24,
    flexDirection: 'column',
    alignItems: 'center',
  },
  textCenter: {
    textAlign: 'center',
  },
  usernameBox: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
  },
  headerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 999,
    marginTop: rt.insets.top,
    paddingHorizontal: 24,
  },
  userIconBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: '100%',
    maxWidth: 240,
  },
}));
