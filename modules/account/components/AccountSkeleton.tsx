import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ProfileSkeleton } from './ProfileSkeleton';
import { Skeleton } from 'moti/skeleton';
import { Spacer } from '@/components/Spacer';

export const AccountSkeleton = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <ProfileSkeleton />

      <Spacer height={24} />

      <View style={styles.tabs}>
        <Skeleton width={100} height={48} />
        <Skeleton width={100} height={48} />
        <Skeleton width={100} height={48} />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {},
  tabs: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
}));
