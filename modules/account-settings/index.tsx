import { HeaderStyle } from '@/components/HeaderStyle';
import { zodResolver } from '@hookform/resolvers/zod';
import { KeyboardAvoidingView, Platform, TouchableOpacity, View } from 'react-native';
import { useForm } from 'react-hook-form';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PasswordSection } from './components/PasswordSection';
import { SignInMethodsSection } from './components/SignInMethodsSection';
import { YourAccountSection } from './components/YourAccountSection';
import { AccountSettingsFormData, accountSettingsSchema } from './schema';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';
import Animated from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { useDeactivateAccountMutation } from '@/apis/user';
import { useCommonStore } from '@/store/common';
import { useActionSheet } from '@expo/react-native-action-sheet';
import { router } from 'expo-router';
import { toastError } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useUserStore } from '@/store/user';

type Props = {};

export const AccountSettings = (props: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  const queryClient = useQueryClient();
  const signOut = useUserStore.use.signOut();
  const { showActionSheetWithOptions } = useActionSheet();
  const setBackdropScreenShow = useCommonStore.use.setBackdropScreenShow();
  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();
  const { mutateAsync: deactivateAccount } = useDeactivateAccountMutation();

  const {
    control,
    formState: { errors },
  } = useForm<AccountSettingsFormData>({
    resolver: zodResolver(accountSettingsSchema),
    defaultValues: {
      email: '',
      phoneNumber: '',
      password: '',
    },
  });

  const handleSignOut = () => {
    queryClient.removeQueries();
    signOut();
    router.dismissTo({
      pathname: '/(app)/(tabs)',
    });
  };

  const handleDeactivateAccountOptions = async (index?: number) => {
    if (index === 2) return;

    try {
      setBackdropScreenShow(true);
      await deactivateAccount({ type: index === 0 ? 'deactivate' : 'permanently_delete' });
      handleSignOut();
    } catch (error) {
      toastError(error);
    } finally {
      setBackdropScreenShow(false);
    }
  };

  const handleDeactivateAccount = () => {
    showActionSheetWithOptions(
      {
        options: ['Deactivate Account', 'Permanently Delete Account', 'Cancel'],
        cancelButtonIndex: 2,
        destructiveButtonIndex: 1,
        userInterfaceStyle: 'dark',
        message:
          "Both options remove your account and content from the Rabid platform immediately. Permanent deletion takes between 30 and 90 days to complete, during which time you may recover your account using  a link we'll email to you. Once an account is permanently deleted, it cannot be recovered.",
        showSeparators: true,
        containerStyle: styles.actionSheetContainer,
        textStyle: styles.actionSheetText,
        messageTextStyle: styles.actionSheetMessageText,
        separatorStyle: styles.separatorStyle,
        cancelButtonTintColor: '#0A84FF',
        destructiveColor: '#FF453A',
        tintColor: '#0A84FF',
      },
      handleDeactivateAccountOptions
    );
  };

  return (
    <View style={styles.container}>
      <HeaderStyle title='Account Settings' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <Animated.ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps='handled'
          contentContainerStyle={[styles.scrollContent, { paddingTop: headerHeight + 24 }]}
          onScroll={onScroll}
        >
          <SignInMethodsSection />
          <YourAccountSection control={control} errors={errors} />
          <PasswordSection control={control} errors={errors} />
        </Animated.ScrollView>
      </KeyboardAvoidingView>

      <View style={styles.signOutContainer}>
        <TouchableOpacity activeOpacity={0.7} onPress={handleDeactivateAccount} style={styles.deactivateButton}>
          <ThemedText type='defaultBold' style={styles.signOutText}>
            Deactivate Account
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  actionSheetContainer: {
    backgroundColor: theme.colors.neutralCard,
    marginHorizontal: 10,
    marginBottom: 40,
    borderRadius: 15.32,
  },
  separatorStyle: {
    width: '100%',
    height: 0.36,
    backgroundColor: '#808080B2',
  },
  actionSheetText: {
    fontSize: 17,
    ...theme.fw400,
    textAlign: 'center',
    width: '100%',
  },
  actionSheetMessageText: {
    color: '#7F7F7F80',
    fontSize: 13,
    ...theme.fw600,
    textAlign: 'center',
  },
  signOutContainer: {
    paddingHorizontal: 24,
    paddingTop: 28,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom + 28,
  },
  deactivateButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    backgroundColor: theme.colors.stateErrorOpacity20,
    borderRadius: 9999,
    gap: 16,
    marginHorizontal: 35,
  },
  signOutText: {
    color: theme.colors.stateError,
    zIndex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    minHeight: rt.screen.height,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 32,
  },
}));
