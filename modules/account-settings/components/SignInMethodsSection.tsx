import { Icons } from '@/assets/icons';
import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { SignInApple, SignInAppleRef } from '@/modules/sign-in/components/SignInApple';
import { SignInFacebook, SignInFacebookRef } from '@/modules/sign-in/components/SignInFacebook';
import { SignInGoogle, SignInGoogleRef } from '@/modules/sign-in/components/SignInGoogle';
import { SignInSpotify, SignInSpotifyRef } from '@/modules/sign-in/components/SignInSpotify';
import { useState, useRef, useCallback } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useGetUserIdentitiesQuery } from '@/apis/user';
import type { IUserIdentity } from '@/apis/user/types';
import { IconLoading } from '@/components/IconLoading';
import { IdentityStatus } from '@/apis/user/types';
import { Tooltip } from '@/components/ui/Tooltip';
import queryKeys from '@/utils/queryKeys';
import { useQueryClient } from '@tanstack/react-query';
import { Show } from '@/components/Show';

export const SignInMethodsSection = () => {
  const { styles, theme } = useStyles(stylesheet);
  const [visible, setVisible] = useState(false);
  const queryClient = useQueryClient();

  const googleRef = useRef<SignInGoogleRef>(null);
  const appleRef = useRef<SignInAppleRef>(null);
  const facebookRef = useRef<SignInFacebookRef>(null);
  const spotifyRef = useRef<SignInSpotifyRef>(null);

  const { data: profile, isLoading: isLoadingProfile } = useGetProfileQuery();

  const { data: identities, isLoading } = useGetUserIdentitiesQuery({ enabled: !!profile?.id });

  const signInMethods = ((identities as IUserIdentity[]) || []).map((identity) => {
    let icon, backgroundColor, borderColor, text, email, status;
    status = identity.status;
    text = status === IdentityStatus.UNTRACKABLE ? identity.providerName : identity.subject;
    switch (identity.providerType) {
      case 'google':
        icon = Icons.Google;
        backgroundColor = theme.colors.neutralWhite;
        borderColor = theme.colors.whiteOpacity20;
        break;
      case 'apple':
        icon = Icons.Apple;
        backgroundColor = theme.colors.neutralWhite;
        borderColor = theme.colors.whiteOpacity20;
        break;
      case 'facebook':
        icon = Icons.Facebook;
        backgroundColor = theme.colors.facebook;
        borderColor = 'transparent';
        email = identity.subject;
        break;
      case 'spotify':
        icon = Icons.Spotify;
        backgroundColor = theme.colors.spotify;
        borderColor = 'transparent';
        break;
      case 'email':
        icon = Icons.Email;
        backgroundColor = 'transparent';
        borderColor = theme.colors.whiteOpacity20;
        break;
      case 'phone':
        icon = Icons.Phone;
        backgroundColor = 'transparent';
        borderColor = theme.colors.whiteOpacity20;
        break;
      default:
        icon = Icons.Email;
        backgroundColor = 'transparent';
        borderColor = theme.colors.whiteOpacity20;
    }
    return { id: identity.providerType, icon, backgroundColor, borderColor, text, email, status };
  });

  const existingProviderTypes = ((identities as IUserIdentity[]) || []).map((identity) => identity.providerType);

  const availableSignInMethods = [
    { component: SignInSpotify, providerType: 'spotify' },
    { component: SignInGoogle, providerType: 'google' },
    { component: SignInApple, providerType: 'apple' },
    { component: SignInFacebook, providerType: 'facebook' },
  ].filter((method) => !existingProviderTypes.includes(method.providerType));

  const handleMethodAdded = () => {
    setVisible(false);
    queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.identities() });
  };

  const handleMethodChanged = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.identities() });
  };

  const handleCancelMethodChange = useCallback(() => {
    setVisible(false);
  }, []);

  const handleChangeMethod = (providerType: string) => {
    if (providerType === 'email' || providerType === 'phone') {
      return;
    }

    switch (providerType) {
      case 'google':
        googleRef.current?.onLogin('change');
        break;
      case 'apple':
        appleRef.current?.onLogin('change');
        break;
      case 'facebook':
        facebookRef.current?.onLogin('change');
        break;
      case 'spotify':
        spotifyRef.current?.onLogin('change');
        break;
    }
  };

  const renderSignInMethod = (method: {
    id: string;
    icon: any;
    backgroundColor: string;
    borderColor: string;
    text?: string;
    email?: string;
    status?: string;
  }): JSX.Element => (
    <View key={method.id} style={[styles.signInMethodContainer]}>
      <View
        style={[
          styles.iconContainer,
          {
            backgroundColor: method.backgroundColor,
            borderColor: method.borderColor,
          },
        ]}
      >
        <method.icon size={24} color={method.id === 'google' || method.id === 'apple' ? '#000' : '#fff'} />
      </View>

      <View style={styles.signInContent}>
        <ThemedText type='default' style={styles.signInText} numberOfLines={1}>
          {method.text}
        </ThemedText>

        <Show when={method.status === IdentityStatus.MISMATCHED && method.id !== 'email' && method.id !== 'phone'}>
          <Tooltip
            popover={
              <View style={{ padding: 16 }}>
                <ThemedText type='tinyMedium' style={{ color: theme.colors.neutralWhite }}>
                  Email used in this method doesn’t match your primary email account. Please update it to sign in.
                </ThemedText>
              </View>
            }
            backgroundColor={theme.colors.neutralDarkGrey}
            autoSize={true}
          >
            <Icons.Warning size={20} color={theme.colors.primary} style={{ marginRight: 8 }} />
          </Tooltip>
        </Show>
      </View>
      <Show when={method.status !== IdentityStatus.MATCHED && method.id !== 'email' && method.id !== 'phone'}>
        {method.status !== IdentityStatus.MATCHED && (
          <TouchableOpacity onPress={() => handleChangeMethod(method.id)}>
            <ThemedText type='defaultMedium' style={{ color: theme.colors.primary }}>
              Change
            </ThemedText>
          </TouchableOpacity>
        )}
      </Show>
    </View>
  );

  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <ThemedText type='defaultSemiBold' style={styles.sectionTitle}>
          Sign In with
        </ThemedText>

        {availableSignInMethods.length > 0 && (
          <TouchableOpacity style={styles.actionButton} onPress={() => setVisible(true)}>
            <ThemedText type='tinySemiBold' style={styles.actionButtonText}>
              Add another Method
            </ThemedText>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.sectionContent}>
        {isLoadingProfile || isLoading ? (
          <IconLoading />
        ) : (
          signInMethods.map(
            (method: {
              id: string;
              icon: any;
              backgroundColor: string;
              borderColor: string;
              text?: string;
              email?: string;
              status?: string;
            }) => renderSignInMethod(method)
          )
        )}
      </View>

      <BottomModal isVisible={visible} onClose={() => setVisible(false)}>
        <View style={styles.modalContentWrapper}>
          <View style={styles.modalContent}>
            <ThemedText type='titleSemiBold' style={styles.modalTitle}>
              Add another Method
            </ThemedText>

            {availableSignInMethods.length > 0 &&
              availableSignInMethods.map((method, index) => {
                const Component = method.component;
                const getRef = () => {
                  switch (method.providerType) {
                    case 'google':
                      return googleRef;
                    case 'apple':
                      return appleRef;
                    case 'facebook':
                      return facebookRef;
                    case 'spotify':
                      return spotifyRef;
                    default:
                      return null;
                  }
                };

                return (
                  <View key={method.providerType} style={styles.itemWrapper}>
                    <Component
                      ref={getRef()}
                      isAddMethodMode={true}
                      onMethodAdded={handleMethodAdded}
                      onMethodCancelled={handleCancelMethodChange}
                    />
                  </View>
                );
              })}
          </View>
        </View>
      </BottomModal>

      {/* Hidden components for change method functionality */}
      <View style={{ opacity: 0, position: 'absolute', pointerEvents: 'none' }}>
        <SignInGoogle
          ref={googleRef}
          onMethodChanged={handleMethodChanged}
          onMethodCancelled={handleCancelMethodChange}
        />
        <SignInApple
          ref={appleRef}
          onMethodChanged={handleMethodChanged}
          onMethodCancelled={handleCancelMethodChange}
        />
        <SignInFacebook
          ref={facebookRef}
          onMethodChanged={handleMethodChanged}
          onMethodCancelled={handleCancelMethodChange}
        />
        <SignInSpotify
          ref={spotifyRef}
          onMethodChanged={handleMethodChanged}
          onMethodCancelled={handleCancelMethodChange}
        />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  sectionTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 20,
  },
  actionButton: {
    backgroundColor: theme.colors.primaryOpacity10,
    paddingHorizontal: 16,
    paddingVertical: 7,
    borderRadius: 9999,
  },
  actionButtonText: {
    color: theme.colors.primary,
  },
  modalTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  modalContentWrapper: {
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  modalContent: {
    gap: 24,
  },
  itemWrapper: {
    height: 48,
    width: '100%',
  },
  sectionContent: {
    paddingBottom: 22,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.whiteOpacity10,
  },
  signInMethodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 49,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: theme.colors.whiteOpacity20,
  },
  signInContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  signInText: {
    color: theme.colors.neutralWhite,
    flexShrink: 1,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
}));
