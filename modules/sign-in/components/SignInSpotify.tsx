import { useLoginMutation, useSpotifyRequestTokenMutation } from '@/apis/auth';
import { useAddMethodSocialMutation, useUpdateMethodSocialMutation } from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { useUserStore } from '@/store/user';
import { APP_SCHEME, env } from '@/utils/const';
import { toastError } from '@/utils/toast';
import { ResponseType, makeRedirectUri, useAuthRequest } from 'expo-auth-session';
import { router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useCallback, useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import AccountUpdatedModal from './AccountUpdatedModal';

WebBrowser.maybeCompleteAuthSession();

const discovery = {
  authorizationEndpoint: 'https://accounts.spotify.com/authorize',
  tokenEndpoint: 'https://accounts.spotify.com/api/token',
};

type Props = {
  isOnlyIcon?: boolean;
  isSignUp?: boolean;
  isAddMethodMode?: boolean;
  onMethodAdded?: () => void;
  onMethodChanged?: () => void;
  onMethodCancelled?: () => void;
};

export interface SignInSpotifyRef {
  onLogin: (params?: any) => void;
}

export const SignInSpotify = forwardRef<SignInSpotifyRef, Props>(
  ({ isOnlyIcon, isSignUp, isAddMethodMode = false, onMethodAdded, onMethodChanged, onMethodCancelled }, ref) => {
    const setAccessToken = useUserStore.use.setAccessToken();
    const setRefreshToken = useUserStore.use.setRefreshToken();

    const { mutateAsync: loginMutation, isPending: isPendingLogin } = useLoginMutation();
    const { mutateAsync: addMethodMutation, isPending: isPendingAddMethod } = useAddMethodSocialMutation();
    const { mutateAsync: updateMethodMutation, isPending: isPendingUpdateMethod } = useUpdateMethodSocialMutation();
    const { mutateAsync: requestTokenMutation, isPending: isPendingRequestToken } = useSpotifyRequestTokenMutation();
    const { styles } = useStyles(stylesheet);

    const [isAccountUpdatedModalVisible, setIsAccountUpdatedModalVisible] = useState(false);
    const [accountUpdatedToken, setAccountUpdatedToken] = useState<string>('');

    const signIn = useCallback(
      async (idToken: string) => {
        try {
          const loginResult = await loginMutation({
            provider: 'spotify',
            token: idToken,
          });
          const { user, tokens } = loginResult;
          setRefreshToken(tokens.refreshToken);
          setAccessToken(tokens.accessToken);

          if (router.canDismiss()) router.dismissAll();
          if (user.status === 'complete') {
            router.replace({ pathname: '/(app)/(tabs)' });
          } else if (user.status === 'update_profile') {
            router.replace({ pathname: '/(app)/update-info' });
          } else if (user.status === 'plan_payment') {
            router.replace({ pathname: '/(app)/choose-plan' });
          } else if (user.status === 'choose_interest') {
            router.replace({ pathname: '/(app)/choose-interest' });
          } else if (user.status === 'choose_podcast') {
            router.replace({ pathname: '/(app)/choose-podcast' });
          }
        } catch (error: any) {
          if (error.message == 'Your email account has been updated') {
            setAccountUpdatedToken(idToken);
            setIsAccountUpdatedModalVisible(true);
            return;
          }
          toastError(error);
        }
      },
      [loginMutation, setAccessToken, setRefreshToken]
    );

    const redirectUriPath = useMemo(() => {
      if (isAddMethodMode) {
        return '(app)/settings/account';
      }
      return isSignUp ? 'sign-up' : 'sign-in';
    }, [isAddMethodMode, isSignUp]);

    const [signInRequest, , promptAsync] = useAuthRequest(
      {
        responseType: ResponseType.Code,
        clientId: env.SPOTIFY_CLIENT_ID,
        scopes: ['user-read-email', 'playlist-modify-public'],
        redirectUri: makeRedirectUri({
          scheme: APP_SCHEME,
          path: redirectUriPath,
        }),
        clientSecret: env.SPOTIFY_CLIENT_SECRET,
      },
      discovery
    );

    const signInMethod = useCallback(
      async (idToken: string, methodType: 'add' | 'change') => {
        try {
          if (methodType === 'add') {
            await addMethodMutation({
              provider: 'spotify',
              token: idToken,
            });
            onMethodAdded?.();
            return;
          } else {
            await updateMethodMutation({
              provider: 'spotify',
              token: idToken,
            });
            onMethodChanged?.();
            return;
          }
        } catch (error: any) {
          if (error.message == 'Your email account has been updated') {
            setAccountUpdatedToken(idToken);
            setIsAccountUpdatedModalVisible(true);
            return;
          }
          toastError(error);
          onMethodCancelled?.();
        }
      },
      [addMethodMutation, updateMethodMutation, onMethodAdded, onMethodChanged, onMethodCancelled]
    );

    const handleSpotifySignIn = useCallback(() => {
      if (!signInRequest) {
        return;
      }

      promptAsync()
        .then(async (result) => {
          if (!result) return;

          if (result?.type === 'error') {
            toastError('Authentication denied');
            return;
          }

          if (result?.type !== 'success') {
            return;
          }

          const { code } = result.params;
          if (!signInRequest?.codeVerifier || !code) return;

          try {
            const { access_token } = await requestTokenMutation({
              code,
              codeVerifier: signInRequest?.codeVerifier,
              redirectUri: makeRedirectUri({
                scheme: APP_SCHEME,
                path: redirectUriPath,
              }),
            });
            await signIn(access_token);
          } catch (error) {
            toastError(error);
          }
        })
        .catch((error) => {
          toastError('Unable to connect to your account');
        });
    }, [signInRequest, promptAsync, requestTokenMutation, signIn, redirectUriPath]);

    const handleSignInMethod = useCallback(
      (methodType: 'add' | 'change' = 'add') => {
        if (!signInRequest?.codeVerifier) {
          return;
        }
        promptAsync()
          .then(async (result) => {
            if (result?.type === 'cancel') {
              onMethodCancelled?.();
              return;
            }

            if (result?.type === 'error') {
              toastError('Authentication denied');
              onMethodCancelled?.();
              return;
            }

            if (result?.type !== 'success') return;
            const params = result?.params;
            const { code } = params;
            if (!code || !signInRequest?.codeVerifier) return;

            const { access_token } = await requestTokenMutation({
              code,
              codeVerifier: signInRequest?.codeVerifier,
              redirectUri: makeRedirectUri({
                scheme: APP_SCHEME,
                path: redirectUriPath,
              }),
            });

            await signInMethod(access_token, methodType);
          })
          .catch((error) => {
            toastError('Unable to connect to your account');
            onMethodCancelled?.();
          });
      },
      [signInRequest, redirectUriPath, signInMethod, promptAsync, onMethodCancelled, requestTokenMutation]
    );

    useImperativeHandle(
      ref,
      () => ({
        onLogin: handleSignInMethod,
      }),
      [handleSignInMethod]
    );

    return (
      <>
        <SignInSocialButton
          disabled={
            !signInRequest || isPendingLogin || isPendingRequestToken || isPendingAddMethod || isPendingUpdateMethod
          }
          onPress={() => (isAddMethodMode ? handleSignInMethod() : handleSpotifySignIn())}
          Icon={<Icons.Spotify size={24} />}
          title='Spotify'
          isOnlyIcon={isOnlyIcon}
          containerStyle={styles.container}
        />
        <AccountUpdatedModal
          isVisible={isAccountUpdatedModalVisible}
          onClose={() => setIsAccountUpdatedModalVisible(false)}
          token={accountUpdatedToken}
          provider='spotify'
        />
      </>
    );
  }
);

SignInSpotify.displayName = 'SignInSpotify';

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.spotify,
  },
}));
