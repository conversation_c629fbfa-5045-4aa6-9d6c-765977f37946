import { ILoginBody, useLoginMutation } from '@/apis/auth';
import { useAddMethodSocialMutation, useUpdateMethodSocialMutation } from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { useUserStore } from '@/store/user';
import { toastError } from '@/utils/toast';
import { router } from 'expo-router';
import { Platform } from 'react-native';
import { AccessToken, AuthenticationToken, LoginManager } from 'react-native-fbsdk-next';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import { forwardRef, useImperativeHandle, useState } from 'react';
import AccountUpdatedModal from './AccountUpdatedModal';

type Props = {
  isOnlyIcon?: boolean;
  isAddMethodMode?: boolean;
  onMethodAdded?: () => void;
  onMethodChanged?: () => void;
  onMethodCancelled?: () => void;
};

export interface SignInFacebookRef {
  onLogin: (params?: any) => void;
}

export const SignInFacebook = forwardRef<SignInFacebookRef, Props>(
  ({ isOnlyIcon, isAddMethodMode = false, onMethodAdded, onMethodChanged, onMethodCancelled }, ref) => {
    const { mutateAsync: loginMutation, isPending: isPendingLogin } = useLoginMutation();
    const { mutateAsync: addMethodMutation, isPending: isPendingAddMethod } = useAddMethodSocialMutation();
    const { mutateAsync: updateMethodMutation, isPending: isPendingUpdateMethod } = useUpdateMethodSocialMutation();

    const setAccessToken = useUserStore.use.setAccessToken();
    const setRefreshToken = useUserStore.use.setRefreshToken();
    const { styles } = useStyles(stylesheet);

    const [isAccountUpdatedModalVisible, setIsAccountUpdatedModalVisible] = useState(false);
    const [accountUpdatedToken, setAccountUpdatedToken] = useState<string>('');
    const [accountUpdatedType, setAccountUpdatedType] = useState<'authentication' | 'access_token'>('authentication');

    const handleGetAccessToken = async () => {
      const result = await LoginManager.logInWithPermissions(['public_profile'], 'limited');
      if (result.isCancelled) {
        throw new Error('Authentication denied');
      }

      if (Platform.OS === 'ios') {
        // This token **cannot** be used to access the Graph API.
        // https://developers.facebook.com/docs/facebook-login/limited-login/
        const result = await AuthenticationToken.getAuthenticationTokenIOS();
        if (!result?.authenticationToken) throw new Error('Authentication denied');

        return {
          token: result.authenticationToken,
          type: 'authentication',
        } as const;
      } else {
        // This token can be used to access the Graph API.
        const result = await AccessToken.getCurrentAccessToken();
        if (!result?.accessToken) throw new Error('Authentication denied');

        return {
          token: result.accessToken,
          type: 'access_token',
        } as const;
      }
    };

    const signIn = async (idToken: string, type: ILoginBody['type']) => {
      const loginResult = await loginMutation({
        provider: 'facebook',
        token: idToken,
        type,
      });
      const { user, tokens } = loginResult;
      setRefreshToken(tokens.refreshToken);
      setAccessToken(tokens.accessToken);

      if (router.canDismiss()) router.dismissAll();
      if (user.status === 'complete') {
        router.replace({ pathname: '/(app)/(tabs)' });
      } else if (user.status === 'update_profile') {
        router.replace({ pathname: '/(app)/update-info' });
      } else if (user.status === 'plan_payment') {
        router.replace({ pathname: '/(app)/choose-plan' });
      } else if (user.status === 'choose_interest') {
        router.replace({ pathname: '/(app)/choose-interest' });
      } else if (user.status === 'choose_podcast') {
        router.replace({ pathname: '/(app)/choose-podcast' });
      }
    };

    const handleFacebookSignIn = async () => {
      let currentToken;
      let currentType;

      try {
        const { token, type } = await handleGetAccessToken();
        currentToken = token;
        currentType = type;
        await signIn(token, type);
      } catch (error: any) {
        if (error.message == 'Your email account has been updated') {
          if (currentToken) {
            if (currentType === 'authentication') {
              setAccountUpdatedToken(currentToken);
              setAccountUpdatedType('authentication');
            } else {
              setAccountUpdatedToken(currentToken);
              setAccountUpdatedType('access_token');
            }
          }

          setIsAccountUpdatedModalVisible(true);
          return;
        }
        toastError(error);
      } finally {
        LoginManager.logOut();
      }
    };

    const handleSignInMethod = async (methodType: 'add' | 'change' = 'add') => {
      let currentToken;
      let currentType;

      try {
        const { token, type } = await handleGetAccessToken();
        currentToken = token;
        currentType = type;

        if (methodType === 'add') {
          await addMethodMutation({
            provider: 'facebook',
            token: token,
            type: type,
          });
          onMethodAdded?.();
        } else {
          await updateMethodMutation({
            provider: 'facebook',
            token: token,
            type: type,
          });
          onMethodChanged?.();
        }
      } catch (error: any) {
        if (error.message == 'Your email account has been updated') {
          if (currentToken) {
            if (currentType === 'authentication') {
              setAccountUpdatedToken(currentToken);
              setAccountUpdatedType('authentication');
            } else {
              setAccountUpdatedToken(currentToken);
              setAccountUpdatedType('access_token');
            }
          }

          setIsAccountUpdatedModalVisible(true);
          return;
        }
        toastError(error);
        onMethodCancelled?.();
      }
    };

    useImperativeHandle(ref, () => ({
      onLogin: handleSignInMethod,
    }));

    return (
      <>
        <SignInSocialButton
          disabled={isPendingLogin || isPendingAddMethod || isPendingUpdateMethod}
          onPress={() => (isAddMethodMode ? handleSignInMethod('add') : handleFacebookSignIn())}
          Icon={<Icons.Facebook size={24} />}
          title='Facebook'
          containerStyle={styles.container}
          isOnlyIcon={isOnlyIcon}
          textStyle={styles.text}
        />
        <AccountUpdatedModal
          isVisible={isAccountUpdatedModalVisible}
          onClose={() => setIsAccountUpdatedModalVisible(false)}
          token={accountUpdatedToken}
          provider='facebook'
          type={accountUpdatedType}
        />
      </>
    );
  }
);

SignInFacebook.displayName = 'SignInFacebook';

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.facebook,
  },
  text: {
    color: theme.colors.neutralWhite,
  },
}));
