import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { CustomButton } from '@/components/ui/CustomButton';
import { useSubscriptionContext } from '@/contexts/subscription.context';
import { format } from 'date-fns';
import { router } from 'expo-router';
import { Skeleton } from 'moti/skeleton';
import { useMemo, useState } from 'react';
import { Linking, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {};

export const CurrentPlan = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const { isPurchasing, customerInfo } = useSubscriptionContext();

  const premium = customerInfo?.entitlements.active['premium'];
  const isCancelled = premium && (!premium?.willRenew || !!premium?.unsubscribeDetectedAt);

  const premiumExpired = premium?.expirationDate;
  const premiumCancelled = !premium?.willRenew;
  const premiumActive =
    premium?.isActive || (!premiumCancelled && !!premiumExpired && new Date(premiumExpired) > new Date());

  const handleShowModal = (isShow: boolean) => {
    setIsModalVisible(isShow);
  };

  const handleAction = () => {
    if (!premium || isCancelled) {
      return router.push({
        pathname: '/(app)/choose-plan',
        params: { isAccountUpdate: 'true' },
      });
    }

    // confirm cancel subscription
    handleShowModal(true);
  };

  const handleCancel = () => {
    handleShowModal(false);
  };

  const handleCancelSubscription = async () => {
    handleShowModal(false);
    const currentStore = premium?.store;

    if (customerInfo?.managementURL) {
      Linking.openURL(customerInfo?.managementURL);
    } else if (currentStore === 'APP_STORE') {
      Linking.openURL('https://apps.apple.com/account/subscriptions');
    } else if (currentStore === 'PLAY_STORE') {
      Linking.openURL('https://play.google.com/store/account/subscriptions');
    }
  };

  const currentPlan = premiumActive ? 'Founding Patron' : 'Founding Member';

  const actionTitle = useMemo(() => {
    if (isCancelled) return 'Resubscribe';

    if (premiumActive) return 'Cancel Subscription';

    return 'Upgrade Subscription';
  }, [premiumActive, isCancelled]);

  const expiredTime = useMemo(() => {
    if (!premiumExpired) return '';

    if (isCancelled)
      return `You'll keep your benefits until ${format(new Date(premiumExpired), 'MMM dd, yyyy')}, & won't be charged again`;

    if (premiumActive) return `Renew on ${format(new Date(premiumExpired), 'MMM dd, yyyy')}`;

    return '';
  }, [premiumExpired, premiumActive, isCancelled]);

  if (!customerInfo)
    return (
      <View style={styles.container}>
        <Skeleton width={150} height={28} />

        <Skeleton width={153} height={38} radius={999} />
      </View>
    );

  return (
    <View style={styles.container}>
      <View style={styles.planInfo}>
        <ThemedText type='subtitleSemiBold'>{currentPlan}</ThemedText>

        <Show when={premiumActive}>
          <Spacer height={8} />

          <ThemedText type='smallNormal'>{expiredTime}</ThemedText>
        </Show>
      </View>

      <View>
        <CustomButton style={styles.actionButton} textType='tinySemiBold' onPress={handleAction}>
          {actionTitle}
        </CustomButton>
      </View>

      <ConfirmationModal
        isVisible={isModalVisible}
        description=''
        title='Are you sure you want to cancel subscription?'
        onCancel={handleCancel}
        onClose={() => {
          handleShowModal(false);
        }}
        onConfirm={handleCancelSubscription}
        cancelText='Nevermind'
        confirmText='Cancel Subscription'
        isLoading={isPurchasing}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    borderColor: theme.colors.neutralBackground,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  planInfo: {
    flex: 1,
  },
  actionButton: {
    minHeight: 38,
  },
}));
