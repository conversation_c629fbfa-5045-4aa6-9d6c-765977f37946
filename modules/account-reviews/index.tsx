import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { Header } from '@/components/ui/Header';
import { useIsYou } from '@/hooks/useIsYou';
import { useLocalSearchParams } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { UserReviewAll } from './components/tabs/UserReviewAll';
import { useCallback } from 'react';
import { ReviewHeader } from './components/ReviewHeader';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';
import { truncateHeader } from '@/utils/func';

type Props = {};

export type TReviewTab = 'All' | 'Episodes' | 'Show';

export const AccountReviews = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  const renderHeader = useCallback(() => {
    return <ReviewHeader userId={userId} />;
  }, [userId]);

  const title = isPendingUserProfile ? '' : isYou ? 'My Reviews' : `${userProfileById?.username} Reviews`;
  const titleTruncated = truncateHeader(title || '');

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header title={titleTruncated} isBack />
      </View>

      <TabContainer
        containerStyle={styles.tabContainer}
        headerContainerStyle={styles.headerContainer}
        renderHeader={renderHeader}
      >
        <TabWrapper tabName={'All'} label={'All'}>
          <UserReviewAll userId={userId} tab={'All'} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName={'Show'} label={'Show'}>
          <UserReviewAll userId={userId} tab={'Show'} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName={'Episodes'} label={'Episodes'}>
          <UserReviewAll userId={userId} tab={'Episodes'} profile={userProfileById} />
        </TabWrapper>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
}));
