import {
  useDeleteReplyCommentEpisodeMutation,
  useDeleteReplyCommentMutation,
  useReportReplyCommentMutation,
  useReportReplyEpisodeCommentMutation,
} from '@/apis/comment/mutations';
import { ICommentReply, ICommentResponse } from '@/apis/comment/types';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { useIsYou } from '@/hooks/useIsYou';
import DeleteCommentModal from '@/modules/show-detail/components/DeleteCommentModal';
import { formatIntervalToNowDuration } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { router } from 'expo-router';
import { useCallback, useState } from 'react';
import { Pressable, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { UserProfileText } from '@/components/UserProfileText';
import { Show } from '@/components/Show';
import CommentMenu from '@/modules/show-detail/components/CommentMenu';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { useUserStore } from '@/store/user';
import { ReportModal } from '@/components/ReportModal';
import { reportReplyCommentCache } from '@/apis/settled-handler/comment';
import { useQueryClient } from '@tanstack/react-query';

type Props = {
  postInfo?: ICommentResponse;
  commentData: ICommentReply;
  podcastId?: string;
  episodeId?: string;
  onRefetch: () => void;
};

export const Comment = ({ commentData, postInfo, podcastId, episodeId, onRefetch }: Props) => {
  const { user, content, updatedAt, id, hasReported } = commentData;
  const { styles, theme } = useStyles(stylesheet);
  const [openMenu, setOpenMenu] = useState(false);
  const [showDeleteCommentModal, setShowDeleteCommentModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  const queryClient = useQueryClient();

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const setIsShowWarningRestricted = useUserStore.use.setIsShowWarningRestricted();

  const { mutateAsync: reportReplyComment, isPending: isReporting } = useReportReplyCommentMutation();
  const { mutateAsync: reportReplyEpisodeComment, isPending: isReportingEpisode } =
    useReportReplyEpisodeCommentMutation();

  const isOwner = useIsYou({
    userId: commentData.user.id.toString(),
  });

  const { mutateAsync: deleteComment, isPending: isPendingDeleteComment } = useDeleteReplyCommentMutation();

  const { mutateAsync: deleteCommentEpisode, isPending: isPendingDeleteCommentEpisode } =
    useDeleteReplyCommentEpisodeMutation();

  const handlerDeleteComment = async () => {
    if (!postInfo) return;
    try {
      if (episodeId) {
        await deleteCommentEpisode({
          id: commentData.id,
          commentId: postInfo?.id,
          source: postInfo?.source,
        });
      } else {
        await deleteComment({
          id: commentData.id,
          commentId: postInfo?.id,
          source: postInfo?.source,
        });
      }
      setShowDeleteCommentModal(false);
      toastSuccess({
        description: 'Deleted post successfully',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleConfirmDeletePost = (isShow: boolean) => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) {
      setIsShowWarningRestricted(true);
      return;
    }
    setShowDeleteCommentModal(isShow);
  };

  const handleToggleMenuDelete = () => {
    handleConfirmDeletePost(true);
    setOpenMenu(false);
  };

  const handleEditPost = () => {
    setOpenMenu(false);

    if (!postInfo) return; // Don't navigate if postInfo is not available

    if (episodeId) {
      // Route for episode comment reply
      router.push({
        pathname: '/(app)/episode/[episodeId]/review/[postId]/reply',
        params: {
          episodeId,
          postId: postInfo?.id?.toString() ?? '',
          isEdit: 'true',
          postInfo: JSON.stringify(postInfo),
          initCommentContent: content,
          commentId: id,
        },
      });
    } else if (podcastId) {
      // Route for podcast comment reply
      router.push({
        pathname: '/(app)/podcast/[podcastId]/review/[postId]/reply',
        params: {
          podcastId,
          postId: postInfo?.id?.toString() ?? '',
          isEdit: 'true',
          postInfo: JSON.stringify(postInfo),
          initCommentContent: content,
          commentId: id,
        },
      });
    }
  };

  const handleConfirmReport = useCallback(() => {
    setShowConfirmationModal(true);
  }, []);

  const handleCloseConfirmReport = useCallback(() => {
    setShowConfirmationModal(false);
  }, []);

  const handleReportComment = async (reasons: string[]) => {
    if (!postInfo) return;

    try {
      if (episodeId) {
        await reportReplyEpisodeComment({
          replyId: id,
          reasons,
          source: postInfo?.source,
        });
        handleCloseConfirmReport();
        toastSuccess({ description: 'You Report This Post' });
      } else {
        await reportReplyComment({
          replyId: id,
          reasons,
          source: postInfo?.source,
        });
        handleCloseConfirmReport();
        toastSuccess({ description: 'You Report This Post' });
      }
    } catch (error) {
      toastError(error);
    }
  };

  const handleViewCommentReported = () => {
    reportReplyCommentCache(queryClient, {
      id: id,
      isReport: false,
      isEpisode: false,
    });
  };

  if (hasReported)
    return (
      <View style={styles.commentReportContainer}>
        <ThemedText type='tinyMedium' style={styles.reportTextTitle}>
          You reported this post.
        </ThemedText>

        <Pressable onPress={handleViewCommentReported} style={({ pressed }) => ({ opacity: pressed ? 0.7 : 1 })}>
          <ThemedText type='tinyMedium'>View</ThemedText>
        </Pressable>
      </View>
    );

  return (
    <View style={styles.container}>
      <UserProfileTouch userId={user?.id} userType={postInfo?.source}>
        <Avatar image={user?.avatar} size={40} />
      </UserProfileTouch>

      <View style={styles.commentBox}>
        <View style={styles.headerComment}>
          <UserProfileText userId={user?.id} userType={postInfo?.source} style={styles.fullFlex}>
            <ThemedText type='tinyMedium' style={[styles.textOpacity, isOwner && styles.textPrimary, styles.fullFlex]}>
              {isOwner ? 'You' : user?.username}
            </ThemedText>
          </UserProfileText>

          <View style={[styles.rowCenter, styles.timeRightBox]}>
            <ThemedText type='tinyMedium' style={styles.textOpacity}>
              {formatIntervalToNowDuration(updatedAt)}
            </ThemedText>

            <Show when={!isOwner}>
              <CommentMenu
                open={openMenu}
                setOpen={setOpenMenu}
                onDeletePost={handleToggleMenuDelete}
                onEditPost={handleEditPost}
                onReportPost={handleConfirmReport}
                isOwner={isOwner}
              >
                <Icons.EllipsisVertical size={24} color={'rgba(255, 255, 255, 0.3)'} />
              </CommentMenu>
            </Show>
          </View>
        </View>

        <ThemedText type='small'>
          {content}
          {commentData.updatedAt !== commentData.createdAt && (
            <ThemedText type='tinyMedium' style={styles.commentEditedText}>
              {' '}
              (Edited)
            </ThemedText>
          )}
        </ThemedText>
      </View>

      <DeleteCommentModal
        visible={showDeleteCommentModal}
        onClose={() => handleConfirmDeletePost(false)}
        onDelete={handlerDeleteComment}
        isPendingDelete={isPendingDeleteComment || isPendingDeleteCommentEpisode}
      />

      <ReportModal
        isVisible={showConfirmationModal}
        onClose={handleCloseConfirmReport}
        onConfirm={handleReportComment}
        isLoading={isReporting || isReportingEpisode}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  reportTextTitle: {
    color: theme.colors.whiteOpacity56,
    ...theme.fw500,
  },
  commentReportContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 24,
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
    minHeight: 50,
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerComment: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    gap: 24,
    flex: 1,
  },
  container: {
    flexDirection: 'row',
    gap: 16,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 999,
  },
  info: {
    gap: 16,
  },
  textOpacity: {
    opacity: 0.56,
  },
  commentBox: {
    flex: 1,
    gap: 4,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  commentEditedText: {
    color: theme.colors.neutralLightGrey,
  },
  fullFlex: {
    flex: 1,
  },
  timeRightBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
}));
