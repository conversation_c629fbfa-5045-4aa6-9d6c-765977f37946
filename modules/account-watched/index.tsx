import { Header } from '@/components/ui/Header';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useLocalSearchParams } from 'expo-router';
import { useIsYou } from '@/hooks/useIsYou';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { WatchedAll } from './components/WatchedAll';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';
import { truncateHeader } from '@/utils/func';

export const AccountWatched = () => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  const title = isPendingUserProfile ? '' : isYou ? 'My Watched' : `${userProfileById?.username} Watched`;
  const titleTruncated = truncateHeader(title || '');

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header title={titleTruncated} isBack />
      </View>

      <TabContainer containerStyle={styles.tabContainer}>
        <TabWrapper tabName='All' label={'All'}>
          <WatchedAll userId={userId} tab={'All'} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName='Shows' label={'Shows'}>
          <WatchedAll userId={userId} tab={'Shows'} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName='Episodes' label={'Episodes'}>
          <WatchedAll userId={userId} tab={'Episodes'} profile={userProfileById} />
        </TabWrapper>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
}));
