import { IUserProfileResponse } from '@/apis/auth/types';
import DateTimePickerInput from '@/components/ui/DateTimePicker';
import { TextareaInput } from '@/components/ui/TextareaInput';
import TextInput from '@/components/ui/TextInput';
import { UpdateUserProfileZodData } from '@/lib/validations/auth';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  control: Control<UpdateUserProfileZodData>;
  errors: FieldErrors<UpdateUserProfileZodData>;
  userProfile?: IUserProfileResponse;
};

export const ProfileFormSection = ({ control, errors, userProfile }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Controller
        control={control}
        name='username'
        rules={{
          required: 'Username is required',
        }}
        render={({ field: { onChange, value } }) => (
          <TextInput
            label='Username'
            placeholder='Please Enter Your Name'
            value={value}
            onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
            error={errors.username?.message}
            containerStyle={styles.inputSpacing}
          />
        )}
      />

      <Controller
        control={control}
        name='email'
        rules={{
          required: 'Email is required',
          pattern: {
            value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Invalid email address',
          },
        }}
        render={({ field: { onChange, value } }) => (
          <TextInput
            label='Email'
            placeholder='Input Your Email'
            value={value}
            onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
            error={errors.email?.message}
            keyboardType='email-address'
            autoCapitalize='none'
            containerStyle={styles.inputSpacing}
          />
        )}
      />

      <Controller
        control={control}
        name='phoneNumber'
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label='Phone Number (Optional)'
            placeholder='Input Phone Number'
            onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
            onBlur={onBlur}
            value={value}
            autoCapitalize='none'
            error={errors.phoneNumber?.message}
          />
        )}
      />

      <Controller
        control={control}
        name='dateOfBirth'
        render={({ field: { onChange, value } }) => (
          <DateTimePickerInput
            label='Date of Birth (Optional)'
            placeholder='MM/DD/YYYY'
            value={value ?? ''}
            onChange={onChange}
            error={errors.dateOfBirth?.message}
            containerStyle={styles.inputSpacing}
            labelStyle={styles.label}
          />
        )}
      />

      <Controller
        control={control}
        name='address'
        render={({ field: { onChange, value } }) => (
          <TextareaInput
            label='Address (Optional)'
            placeholder='Please Enter Your Address'
            value={value}
            onChangeText={(value) => onChange(value)}
            error={errors.address?.message}
            containerStyle={styles.inputSpacing}
            numberOfLines={5}
            inputContainer={{ minHeight: 72 }}
          />
        )}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    gap: 32,
  },
  inputSpacing: {
    marginTop: 0,
  },
  label: {
    marginBottom: 16,
  },
}));
