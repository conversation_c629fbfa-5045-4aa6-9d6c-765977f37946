import { useGetProfileQuery } from '@/apis/auth/queries';
import { Icons } from '@/assets/icons';
import DeleteOutline from '@/assets/icons/delete-outline';
import EditOutline from '@/assets/icons/edit-outline';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { DropdownMenu, MenuOption } from '@/components/dropdown-menu';
import { Image } from 'expo-image';
import { PropsWithChildren, useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface CommentMenuProps {
  isOwner: boolean;
  open: boolean;
  setOpen: (open: boolean) => void;
  onEditPost: () => void;
  onDeletePost: () => void;
  onReportPost: () => void;
  isReporting?: boolean;
}

function CommentMenu({
  isOwner,
  open,
  setOpen,
  onEditPost,
  onDeletePost,
  onReportPost,
  children,
}: PropsWithChildren<CommentMenuProps>) {
  const { styles, theme } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const isPremium = userProfile?.type === 'premium';

  const handleCloseDropdown = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const handleOpenDropDown = () => {
    setOpen(true);
  };

  const handleConfirmReport = useCallback(() => {
    handleCloseDropdown();
    onReportPost();
  }, [handleCloseDropdown, onReportPost]);

  return (
    <View>
      <DropdownMenu
        visible={open}
        handleOpen={handleOpenDropDown}
        handleClose={handleCloseDropdown}
        trigger={children}
        contentStyle={styles.contentContainer}
      >
        <Show when={isOwner}>
          <MenuOption
            onSelect={onEditPost}
            style={[styles.menuOption, !isPremium && styles.actionDisable]}
            disabled={!isPremium}
          >
            <EditOutline />

            <ThemedText style={styles.buttonText}>Edit Post</ThemedText>

            <Image style={{ width: 20, height: 20 }} source={require('@/assets/images/premium_icon.png')} />
          </MenuOption>

          <View style={styles.divider} />

          <MenuOption onSelect={onDeletePost} style={styles.menuOption}>
            <DeleteOutline color={theme.colors.stateError} />

            <ThemedText style={[styles.buttonText, styles.deleteButtonText]}>Delete Post</ThemedText>
          </MenuOption>
        </Show>

        <Show when={!isOwner}>
          <MenuOption onSelect={handleConfirmReport} style={styles.menuOption}>
            <Icons.WarningOutline size={24} color={'#fff'} />

            <ThemedText style={styles.buttonText}>Report Post</ThemedText>
          </MenuOption>
        </Show>
      </DropdownMenu>
    </View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  confirmDescriptionText: {
    color: theme.colors.whiteOpacity64,
  },
  contentContainer: {},
  content: {},
  optionsContainerStyle: {
    marginTop: 52,
    backgroundColor: theme.colors.neutralDarkGrey,
    borderRadius: 8,
  },
  menuOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    gap: 8,
  },
  buttonText: {
    color: theme.colors.neutralWhite,
    ...theme.fw600,
    fontSize: 12,
    lineHeight: 12,
  },
  deleteButtonText: {
    color: theme.colors.stateError,
  },
  divider: { height: 2, width: '100%', backgroundColor: theme.colors.neutralGrey },
  actionDisable: {
    opacity: 0.5,
  },
}));

export default CommentMenu;
