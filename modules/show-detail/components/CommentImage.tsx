import { ExpoImage } from '@/components/ui/Image';
import { router } from 'expo-router';
import { useState } from 'react';
import { FlatList, LayoutChangeEvent, Pressable, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface CommentImageProps {
  images: string[];
  paddingLeft?: number;
}

function CommentImage({ images, paddingLeft = 24 }: CommentImageProps) {
  const { styles } = useStyles(stylesheet);
  const [containerWidth, setContainerWidth] = useState(0);

  const handlePressImage = (index: number) => {
    router.push({
      pathname: '/(modal)/images',
      params: {
        urls: JSON.stringify(images),
        activeIndex: index,
      },
    });
  };

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
  };

  const imageSize = images.length === 1 ? containerWidth - paddingLeft : containerWidth / 2 - paddingLeft;

  return (
    <View onLayout={handleLayout}>
      <FlatList
        bounces={false}
        data={images}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        contentContainerStyle={styles.imagesContainer}
        renderItem={({ item, index }) => (
          <Animated.View
            style={{
              width: imageSize,
              height: Math.min(imageSize, containerWidth / 2),
            }}
            key={index}
          >
            <Pressable
              onPress={() => handlePressImage(index)}
              style={[
                {
                  width: '100%',
                  height: '100%',
                },
              ]}
            >
              <ExpoImage source={{ uri: item }} style={styles.commentImage} contentFit='cover' />
            </Pressable>
          </Animated.View>
        )}
      />
    </View>
  );
}

const stylesheet = createStyleSheet(() => ({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    right: 2,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    opacity: 0.5,
  },
  overlay: {
    borderRadius: 20,
    padding: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  imagesContainer: {
    gap: 4,
  },
  imageWrapper: {
    aspectRatio: 1,
  },
  commentImage: {
    flex: 1,
    borderRadius: 8,
  },
  scrollViewContent: {
    gap: 8,
  },
  singleImageWrapper: {
    height: 154,
    width: '100%',
  },
  singleImage: {
    height: 154,
    width: '100%',
    borderRadius: 8,
  },
  multipleImageWrapper: {
    height: 154,
    width: 154,
  },
  multipleImage: {
    height: 154,
    width: 154,
    borderRadius: 8,
  },
}));

export default CommentImage;
