import { useGetProfileQuery } from '@/apis/auth/queries';
import { planPaymentMutation, useUpdateUserProfileMeMutation } from '@/apis/user';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { TextareaInput } from '@/components/ui/TextareaInput';
import { toastError } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SetAddressFormData, setAddressSchema } from './schema';

type Props = {};

const SetAddress = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { data: userProfile, refetch: refetchProfile, isRefetching: isRefetchingProfile } = useGetProfileQuery();
  const accountCompleteStep = userProfile?.status === 'complete';

  const { mutateAsync: planPayment, isPending: isPendingPlanPayment } = planPaymentMutation();
  const { mutateAsync: updateUserProfile, isPending: isUpdatingUserProfile } = useUpdateUserProfileMeMutation();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SetAddressFormData>({
    defaultValues: {},
    resolver: zodResolver(setAddressSchema),
  });

  const handleMaybeLater = async () => {
    try {
      await planPayment({
        status: 'choose_interest',
      });

      await refetchProfile();

      router.replace({
        pathname: '/(app)/(tabs)',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const onSubmit = async (data: SetAddressFormData) => {
    try {
      await updateUserProfile({
        address: data.address,
      });

      if (accountCompleteStep) {
        await refetchProfile();
        router.back();
      } else {
        await planPayment({
          status: 'choose_interest',
        });
        await refetchProfile();
        router.replace({
          pathname: '/(app)/(tabs)',
        });
      }
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <View style={styles.container}>
      <Header isBack={accountCompleteStep} />

      <KeyboardAwareScrollView
        style={styles.content}
        // contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText style={styles.title}>Setup Your Address</ThemedText>

        <Spacer height={16} />

        <ThemedText type='smallNormal' style={styles.description}>
          Kindly provide the shipping address where you’d like us to send your exclusive Rabid Coin. You can always
          review or update this address later in your User Profile
        </ThemedText>

        <Spacer height={40} />

        <Controller
          control={control}
          name='address'
          render={({ field: { onChange, onBlur, value } }) => (
            <TextareaInput
              placeholder='e.g. 123 Main Street, Apt. 2B, Springfield, Illinois'
              value={value || ''}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.address?.message}
              multiline
              numberOfLines={4}
              // containerStyle={styles.inputSpacing}
            />
          )}
        />
      </KeyboardAwareScrollView>

      <View style={styles.bottom}>
        <CustomButton
          type='primary'
          textType='defaultBold'
          onPress={handleSubmit(onSubmit)}
          isLoading={isUpdatingUserProfile}
        >
          Save
        </CustomButton>

        {!accountCompleteStep && (
          <>
            <Spacer height={24} />

            <CustomButton
              type='text'
              disabled={isRefetchingProfile || isPendingPlanPayment}
              onPress={handleMaybeLater}
              textType='defaultBold'
              textStyle={styles.maybeLaterText}
            >
              Maybe Later
            </CustomButton>
          </>
        )}
      </View>
    </View>
  );
};

export default SetAddress;

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  content: {
    paddingTop: 52,
    paddingBottom: 56,
  },
  bottom: {
    paddingTop: 24,
    paddingBottom: 24,
  },
  maybeLaterText: {
    color: theme.colors.primary,
  },
  title: {
    fontSize: 24,
    lineHeight: 32,
    ...theme.fw600,
  },
  description: {
    color: theme.colors.neutralLightGrey,
  },
}));
