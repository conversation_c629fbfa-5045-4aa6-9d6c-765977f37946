import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { Image, ScrollView, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import FixedButtons from './components/fixed-buttons';
import FoundingPatronCard from './components/founding-patron-card';
import FreeCard from './components/free-card';
import { useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { useSubscriptionContext } from '@/contexts/subscription.context';

const PATREON_PRODUCT_ID = 'patron:p1w';

export default function ChoosePlan() {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();
  const isAccountUpdate = localParams?.isAccountUpdate as string;

  const [bottomFixed, setBottomFixed] = useState(0);

  const { packages } = useSubscriptionContext();

  return (
    <SafeAreaView edges={['top']} style={styles.container}>
      <View style={styles.paddingH}>
        <Header isBack={isAccountUpdate === 'true'} />
      </View>

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={[styles.contentContainer, { paddingBottom: bottomFixed + 24 }]}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText style={styles.headerText}>Join Rabid, Unlock Special Profile & Your Exclusive Coin.</ThemedText>

        <View style={styles.coinContainer}>
          <Image source={require('@/assets/images/rabid_coin.png')} style={styles.coinImage} resizeMode='contain' />
        </View>

        {/* Founding Patron Plan */}

        {packages.map((item) => (
          <FoundingPatronCard key={item.identifier} packageItem={item} />
        ))}

        {/* Free Plan */}
        <FreeCard />

        <ThemedText style={styles.termsText}>
          By subscribing to any plan, you agree that the Free Plan provides full access at no cost, with features
          subject to modification for service improvements. The Founder Plan is a voluntary contribution that supports
          development but does not grant additional features. Founder Plan payments are recurring, non-refundable, and
          cancellations take effect at the end of the billing cycle. While we strive to enhance our platform, specific
          feature updates are not guaranteed. Users must engage responsibly, and misuse may result in suspension. We may
          update these terms periodically, and continued use constitutes acceptance of any changes. For questions,
          contact our support team. 🚀
        </ThemedText>
      </ScrollView>

      <View
        style={styles.buttonContainer}
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          setBottomFixed(height);
        }}
      >
        <FixedButtons />
      </View>
    </SafeAreaView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderColor: theme.colors.neutralDarkGrey,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  headerText: {
    fontSize: 24,
    ...theme.fw600,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 32,
    marginTop: 28,
  },
  coinContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginVertical: 24,
  },
  coinImage: {
    width: 280,
    height: 298,
    zIndex: 1,
  },
  termsText: {
    fontSize: 12,
    opacity: 0.4,
    ...theme.fw500,
    lineHeight: 18,
  },
  paddingH: {
    paddingHorizontal: 24,
  },
}));
