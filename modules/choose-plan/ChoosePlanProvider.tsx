import { PropsWithChildren, useCallback, useEffect, useState } from 'react';
import { SubscriptionContextProvider } from '@/contexts/subscription.context';
import { Platform } from 'react-native';
import Purchases, { CustomerInfo, LOG_LEVEL, PurchasesPackage } from 'react-native-purchases';
import { env } from '@/utils/const';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { toastError, toastSuccess } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { router } from 'expo-router';
import { IUserProfileById, useCancelPurchaseMutation } from '@/apis/user';
import { useCommonStore } from '@/store/common';
import { useUserStore } from '@/store/user';

export const PATRON_PRODUCT_ID = 'patron';

export default function ChoosePlanProvider({ children }: PropsWithChildren) {
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>();
  const [packages, setPackages] = useState<PurchasesPackage[]>([]);
  const [isReady, setIsReady] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const isStagingENV = useCommonStore.use.isStagingENV();
  const setIsShowSetupAddressPatronModal = useUserStore.use.setIsShowSetupAddressPatronModal();
  const hasShownCancelSubscriptionToast = useUserStore.use.hasShownCancelSubscriptionToast();
  const setHasShownCancelSubscriptionToast = useUserStore.use.setHasShownCancelSubscriptionToast();

  const { data: userProfile, refetch } = useGetProfileQuery({ refetchOnMount: true });
  const { mutateAsync: cancelPurchaseRequest } = useCancelPurchaseMutation();

  const queryClient = useQueryClient();

  const loadOfferings = useCallback(async () => {
    const offerings = await Purchases.getOfferings();
    if (offerings.current) {
      setPackages(offerings.current.availablePackages);
    }
  }, []);

  const isUserStatusComplete = userProfile?.status === 'complete';

  const fetchCustomerInfo = useCallback(async () => {
    await Purchases.invalidateCustomerInfoCache();
    const info = await Purchases.getCustomerInfo();
    setCustomerInfo(info);
  }, []);

  const purchasePackage = async (pack: PurchasesPackage) => {
    try {
      setIsPurchasing(true);
      const customerInfoCheck = await Purchases.getCustomerInfo();

      const premium = customerInfoCheck?.subscriptionsByProductIdentifier[PATRON_PRODUCT_ID];

      if (premium?.isActive && premium.willRenew) {
        toastSuccess({
          description: 'You have already subscribed to this plan',
        });

        await refetch();

        return;
      }

      const { productIdentifier, customerInfo: info } = await Purchases.purchasePackage(pack);

      setCustomerInfo(info);

      if (productIdentifier === PATRON_PRODUCT_ID) {
        toastSuccess({
          description: 'Upgrade subscription successfully',
        });

        queryClient.setQueriesData({ queryKey: queryKeys.auth.profile() }, (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            status: isUserStatusComplete ? 'complete' : 'choose_interest',
            type: 'premium',
          };
        });

        queryClient.setQueriesData<IUserProfileById>(
          { queryKey: queryKeys.userProfile.byUserId(userProfile?.id) },
          (oldData) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              type: 'premium',
            };
          }
        );

        if (isUserStatusComplete) {
          router.back();
          if (!userProfile?.address) {
            setIsShowSetupAddressPatronModal(true);
            return;
          }
        } else {
          if (!userProfile?.address) {
            router.replace({
              pathname: '/(app)/set-address',
            });
            return;
          }
        }
      }
    } catch (e: any) {
      if (!e.userCancelled) {
        toastError(e);
      }
    } finally {
      setIsPurchasing(false);
    }
  };

  const updateCustomerInformation = useCallback(async (info: CustomerInfo) => {
    setCustomerInfo(info);
  }, []);

  useEffect(() => {
    const premium = customerInfo?.entitlements?.active['premium'];
    if (!premium) return;

    if (!premium?.willRenew && !hasShownCancelSubscriptionToast) {
      toastSuccess({
        description: 'Your subscription has been canceled',
      });
      setHasShownCancelSubscriptionToast(true);
    } else if (!premium?.isActive) {
      setHasShownCancelSubscriptionToast(false);
      return;
    }
  }, [customerInfo, hasShownCancelSubscriptionToast, setHasShownCancelSubscriptionToast]);

  const restorePermissions = async () => {
    try {
      const customer = await Purchases.restorePurchases();
      return customer;
    } catch (error) {
      toastError(error);
    }
  };

  const cancelPurchase = async () => {
    try {
      setIsPurchasing(true);
      await cancelPurchaseRequest({});
      toastSuccess({ description: 'Your subscription has been canceled' });
    } catch (error) {
      toastError(error);
    } finally {
      setIsPurchasing(false);
    }
  };

  useEffect(() => {
    const init = async () => {
      if (!userProfile?.id) return;

      if (Platform.OS === 'android') {
        await Purchases.configure({
          apiKey: env.REVENUECAT_GOOGLE_API_KEY,
          appUserID: userProfile.id.toString(),
        });
      } else {
        await Purchases.configure({
          apiKey: env.REVENUECAT_APPLE_API_KEY,
          appUserID: userProfile.id.toString(),
        });
      }

      const customerId = `${!isStagingENV ? 'dev' : 'prod'}_${userProfile.id}`;
      const { customerInfo: info } = await Purchases.logIn(customerId);
      setCustomerInfo(info);
      setIsReady(true);

      Purchases.setLogLevel(LOG_LEVEL.INFO);

      Purchases.addCustomerInfoUpdateListener(async (info) => {
        updateCustomerInformation(info);
      });

      await loadOfferings();
    };

    if (!userProfile?.id) return;

    init();

    return () => {
      setCustomerInfo(undefined);
      setIsReady(false);
    };
  }, [loadOfferings, updateCustomerInformation, userProfile?.id, isStagingENV]);

  return (
    <SubscriptionContextProvider
      value={{
        packages,
        isReady,
        customerInfo,
        isPurchasing,
        purchasePackage,
        restorePermissions,
        cancelPurchase,
        fetchCustomerInfo,
      }}
    >
      {children}
    </SubscriptionContextProvider>
  );
}
