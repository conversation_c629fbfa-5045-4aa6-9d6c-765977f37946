import { IBlockedUser, useBlockUserMutation } from '@/apis/user';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { CustomButton } from '@/components/ui/CustomButton';
import { toastError, toastSuccess } from '@/utils/toast';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  blockedUser: IBlockedUser;
};

export const AccountBlockedUserItem = ({ blockedUser }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { mutateAsync: unblockUser, isPending: isPendingUnblock } = useBlockUserMutation();

  const handleUnblockUser = async () => {
    try {
      await unblockUser(blockedUser.id);
      toastSuccess({ description: `You unblocked ${blockedUser.username}` });
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <View style={styles.container}>
      <Avatar image={blockedUser.avatar} size={48} />

      <View style={styles.userDetails}>
        <View style={styles.usernameContainer}>
          <ThemedText style={styles.username} type='defaultSemiBold' numberOfLines={1}>
            {blockedUser.username}
          </ThemedText>

          {blockedUser.type === 'premium' && (
            <View style={styles.verifiedBadge}>
              <Icons.Verify size={18} />
            </View>
          )}
        </View>
      </View>

      <CustomButton
        style={styles.unblock}
        textType='tinySemiBold'
        onPress={handleUnblockUser}
        isLoading={isPendingUnblock}
      >
        Unblock
      </CustomButton>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  userDetails: {
    flex: 1,
  },
  usernameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 34,
  },
  username: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    ...theme.fw500,
    lineHeight: 24,
  },
  verifiedBadge: {
    borderRadius: 999,
    width: 23,
    height: 23,
    paddingTop: 3,
    paddingLeft: 2.5,
    paddingRight: 2.5,
    paddingBottom: 1.5,
    marginLeft: 5.5,
  },
  unblock: {
    minHeight: 38,
  },
}));
