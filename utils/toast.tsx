import { ThemedText } from '@/components/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { Image, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ToastType } from 'toastify-react-native/utils/interfaces';
import { useUserStore } from '@/store/user';

const errorStyles = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.stateError,
    borderRadius: 999,
    gap: 16,
    maxWidth: rt.screen.width - 48,
  },
  imageStyle: {
    width: 16,
    height: 16,
  },
  description: {
    fontSize: 14,
    ...theme.fw500,
    wordWrap: 'break-word',
    maxWidth: rt.screen.width - 48 - 32 - 32,
    width: 'auto',
  },
  safeContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
  },
}));

const successStyles = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.primary,
    borderRadius: 999,
    gap: 16,
    maxWidth: rt.screen.width - 48,
    elevation: 4,
    shadowColor: 'black',
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    shadowOpacity: 0.2,
  },
  imageStyle: {
    width: 16,
    height: 16,
  },
  description: {
    fontSize: 14,
    color: theme.colors.background,
    ...theme.fw500,
    wordWrap: 'break-word',
    maxWidth: rt.screen.width - 48 - 32 - 32,
    width: 'auto',
  },
  safeContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
  },
}));

type IToast = {
  title?: string;
  description: string;
};

const CustomErrorToasterComponent = ({ description }: IToast) => {
  const { styles } = useStyles(errorStyles);
  return (
    <SafeAreaView style={styles.safeContainer}>
      <TouchableOpacity activeOpacity={1} onPressOut={() => Toast.hide()} style={styles.container}>
        <Image source={require('@/assets/images/alert_icon.png')} style={styles.imageStyle} />

        <ThemedText style={styles.description}>{description}</ThemedText>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const CustomSuccessToasterComponent = ({ description }: IToast) => {
  const { styles, theme } = useStyles(successStyles);

  return (
    <SafeAreaView style={styles.safeContainer}>
      <TouchableOpacity activeOpacity={1} onPressOut={() => Toast.hide()} style={styles.container}>
        <Ionicons color={theme.colors.background} name='checkmark-circle' size={24} />

        <ThemedText style={styles.description}>{description}</ThemedText>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export const toastError = (error: any) => {
  const statusCode = error?.code;
  const { setIsShowWarningRestricted } = useUserStore.getState();

  const errorMessage =
    typeof error === 'string' ? error : (error?.message ?? (typeof error === 'string' ? error : 'An error occurred'));

  if (typeof error !== 'string' && statusCode === 403) {
    const data = error?.response?.data as any;
    const meta = data?.meta;
    const cause = meta?.cause;
    const permission = cause?.permission;

    if (permission === 'account_restriction') {
      return setIsShowWarningRestricted(true);
    }
  }

  Toast.hide();
  Toast.show({
    type: 'errorToast' as ToastType,
    props: { description: errorMessage },
    topOffset: 16,
    position: 'top',
    autoHide: true,
    useModal: false,
  });
};

export const toastSuccess = ({ title, description }: IToast) => {
  Toast.hide();
  Toast.show({
    type: 'successToast' as ToastType,
    props: { title, description },
    bottomOffset: 54,
    autoHide: true,
    position: 'bottom',
    useModal: false,
  });
};

export const toastConfig = {
  successToast: ({ props }: any) => {
    const { description, title } = props;
    return <CustomSuccessToasterComponent description={description} title={title} />;
  },
  errorToast: ({ props }: any) => {
    const { description, title } = props;
    return <CustomErrorToasterComponent description={description} title={title} />;
  },
};
