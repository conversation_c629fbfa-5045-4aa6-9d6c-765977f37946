export const ZOD_ERRORS = {
  required: 'Please fill out this field',
  minLength: 'Password must be 8-64 characters',
  maxLength: 'Password must be 8-64 characters',
  pattern: 'Password must include uppercase, lowercase, number, special character, and no space',
  passwordLength: 'Password must be 8-64 characters',
  passwordRegex: 'Password must include uppercase, lowercase, number, special character, and no space',
  passwordMatch: 'Passwords does not match',
  invalidPhoneNumber: 'Invalid phone number. Correct format:\n+[country code][subscriber number]',
  invalidCharsPhoneNumber: 'Phone must only have numbers and "+" for country code, no spaces',
  phoneNumberLength: 'Phone number having country code must be 9-15 digits',
  phoneNumberNoCodeLength: 'Phone number without country code must be 5-14 digits',
  invalidEmail: 'Invalid email format. Correct format:\n[username]@[email provider]',
  emailLength: 'Email address can not exceed 254 characters',
  invalidIdentifier: 'Invalid identifier format',
  usernameLength: 'Username can not exceed 32 characters',
  dateFormat: 'Invalid date. Use MM/DD/YYYY',
  dateOfBirthFormat: 'Invalid date. Please select a valid date of birth.',
  dateOfBirthFuture: 'Date of birth can not be in the future',
  dateOfBirthTooOld: 'The app is only available to users 18 and older.',
  postTitleLength: 'Post title can not exceed 100 characters',
  postContentLength: 'Post content can not exceed 500 characters',
  postImagesLength: 'You can only select max 5 images',
  bioLength: 'Bio can not exceed 600 characters',
  dateOfBirthAge: 'The app only available to users 18 and older.',
  addressLength: 'Address can not exceed 300 characters',
  feedbackLength: 'Feedback cannot exceed 1000 characters',
};
