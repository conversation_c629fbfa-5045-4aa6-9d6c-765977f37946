import { FeedContextProvider } from '@/contexts/feed.context';
import React, { PropsWithChildren } from 'react';
import 'react-native-get-random-values';
import { v4 as uuid } from 'uuid';

type Props = {};

export const FeedProviderWrapper = ({ children }: PropsWithChildren<Props>) => {
  const [feedListKey, setFeedListKey] = React.useState(() => uuid());

  const handleChangeFeedListKey = React.useCallback(() => {
    const newKey = uuid();
    setFeedListKey(newKey);
  }, []);

  return (
    <FeedContextProvider value={{ feedListKey: feedListKey, randomFeedListKey: handleChangeFeedListKey }}>
      {children}
    </FeedContextProvider>
  );
};
