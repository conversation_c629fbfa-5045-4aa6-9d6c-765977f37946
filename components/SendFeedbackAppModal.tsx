import { createStyleSheet, mq, useStyles } from 'react-native-unistyles';
import { useUserStore } from '@/store/user';
import { ModalRn } from './Modal';
import { Keyboard, Platform, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { Image } from 'expo-image';
import { ThemedText } from './ThemedText';
import { Spacer } from './Spacer';
import { CustomButton } from './ui/CustomButton';
import { useCallback, useEffect, useState } from 'react';
import { z } from 'zod';
import { ZOD_ERRORS } from '@/config/zodError';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TextareaInput } from './ui/TextareaInput';
import { useUserFeedbackMutation } from '@/apis/user';
import { toastError } from '@/utils/toast';
import * as StoreReview from 'expo-store-review';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useQueryClient } from '@tanstack/react-query';
import { userFeedbackCache } from '@/apis/settled-handler/user';

type Props = {
  isVisible: boolean;
};

const STEPS = {
  IOS_ENJOY_APP: 1,
  IOS_GIVE_FEEDBACK: 2,
  ANDROID_RATE_APP: 3,
  FEEDBACK: 4,
  THANKS: 5,
} as const;

type TStep = (typeof STEPS)[keyof typeof STEPS];

// Android Step 3 -> 4 -> 5
// iOS Step 1 -> 2 -> 4 -> 5

export const SendFeedbackAppModal = ({ isVisible }: Props) => {
  const { styles } = useStyles(stylesheet);
  const setIsShowedFeedbackAppModal = useUserStore.use.setIsShowedFeedbackAppModal();

  const [step, setStep] = useState<TStep>(() => {
    return Platform.OS === 'android' ? STEPS.ANDROID_RATE_APP : STEPS.IOS_ENJOY_APP;
  });

  const queryClient = useQueryClient();

  const handleClose = () => {
    setIsShowedFeedbackAppModal(false);
  };

  useEffect(() => {
    if (!isVisible) {
      setStep(Platform.OS === 'android' ? STEPS.ANDROID_RATE_APP : STEPS.IOS_ENJOY_APP);
      userFeedbackCache(queryClient);
    }
  }, [isVisible, queryClient]);

  const handleChangeStep = useCallback((newStep: TStep) => {
    setStep(newStep);
  }, []);

  return (
    <ModalRn
      isVisible={isVisible}
      backdropColor={'rgba(0, 0, 0, 0.7)'}
      style={styles.modal}
      contentStyle={styles.container}
    >
      <KeyboardAvoidingView behavior='padding'>
        <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
          <View>
            {step === STEPS.IOS_ENJOY_APP && (
              <SendFeedBackStep1 onChangeStep={handleChangeStep} onClose={handleClose} />
            )}

            {step === STEPS.IOS_GIVE_FEEDBACK && (
              <SendFeedBackStep2 onChangeStep={handleChangeStep} onClose={handleClose} />
            )}

            {step === STEPS.ANDROID_RATE_APP && (
              <SendFeedBackStep3 onChangeStep={handleChangeStep} onClose={handleClose} />
            )}

            {step === STEPS.FEEDBACK && <SendFeedBackStep4 onChangeStep={handleChangeStep} onClose={handleClose} />}

            {step === STEPS.THANKS && <SendFeedBackStep5 onClose={handleClose} />}
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </ModalRn>
  );
};

type SendFeedBackStep1Props = {
  onChangeStep?: (step: TStep) => void;
  onClose?: () => void;
};

const SendFeedBackStep1 = ({ onChangeStep, onClose }: SendFeedBackStep1Props) => {
  const { styles } = useStyles(stylesheet);

  const handleNotReally = () => {
    onChangeStep?.(STEPS.IOS_GIVE_FEEDBACK);
  };

  const handleRequestRateApp = async () => {
    const hasAction = await StoreReview.hasAction();
    const isAvailableAsync = await StoreReview.isAvailableAsync();

    onClose?.();

    if (hasAction && isAvailableAsync) {
      await StoreReview.requestReview();
    }
  };

  return (
    <View style={styles.content}>
      <Image source={require('@/assets/images/logo_hi.png')} style={styles.image} />

      <Spacer height={24} />

      <ThemedText style={styles.title}>Are you enjoying Rabid so far?</ThemedText>

      <Spacer height={16} />

      <ThemedText style={styles.description} type='small'>
        Hi there! We'd love to know if you're having a great experience.
      </ThemedText>

      <Spacer height={32} />

      <View style={styles.buttonContainer}>
        <CustomButton textType='defaultBold' type='primary' style={styles.confirmButton} onPress={handleRequestRateApp}>
          Yes!
        </CustomButton>

        <TouchableOpacity activeOpacity={0.7} onPress={handleNotReally}>
          <ThemedText style={styles.textPrimary} type='defaultBold'>
            Not Really
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const SendFeedBackStep2 = ({ onChangeStep, onClose }: SendFeedBackStep1Props) => {
  const { styles } = useStyles(stylesheet);

  const handleSendFeedback = () => {
    onChangeStep?.(STEPS.FEEDBACK);
  };

  return (
    <View style={styles.content}>
      <Image source={require('@/assets/images/logo_sad.png')} style={styles.image} />

      <Spacer height={24} />

      <ThemedText style={styles.title}>Sorry to hear that</ThemedText>

      <Spacer height={16} />

      <ThemedText style={styles.description} type='small'>
        Please tell us how we can improve the app
      </ThemedText>

      <Spacer height={32} />

      <View style={styles.buttonContainer}>
        <CustomButton textType='defaultBold' type='primary' style={styles.confirmButton} onPress={handleSendFeedback}>
          Give feedback
        </CustomButton>

        <TouchableOpacity activeOpacity={0.7} onPress={onClose}>
          <ThemedText style={styles.textPrimary} type='defaultBold'>
            I'll do it later
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const SendFeedBackStep3 = ({ onChangeStep, onClose }: SendFeedBackStep1Props) => {
  const { styles } = useStyles(stylesheet);

  const handleSendFeedback = () => {
    onChangeStep?.(STEPS.FEEDBACK);
  };

  const handleRequestRateApp = async () => {
    const hasAction = await StoreReview.hasAction();
    const isAvailableAsync = await StoreReview.isAvailableAsync();

    onClose?.();

    if (hasAction && isAvailableAsync) {
      await StoreReview.requestReview();
    }
  };

  return (
    <View style={styles.content}>
      <Image source={require('@/assets/images/logo_hi.png')} style={styles.image} />

      <Spacer height={24} />

      <ThemedText style={styles.title}>Tell us what you think about the app.</ThemedText>

      <Spacer height={32} />

      <View style={styles.buttonContainer}>
        <CustomButton textType='defaultBold' type='primary' style={styles.confirmButton} onPress={handleRequestRateApp}>
          Rate the app
        </CustomButton>

        <TouchableOpacity activeOpacity={0.7} onPress={handleSendFeedback}>
          <ThemedText style={styles.textPrimary} type='defaultBold'>
            Give feedback
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const feedbackSchema = z.object({
  feedback: z
    .string({ message: ZOD_ERRORS.required })
    .trim()
    .min(1, ZOD_ERRORS.required)
    .max(1000, ZOD_ERRORS.feedbackLength),
});
export type FeedbackSchemaData = z.infer<typeof feedbackSchema>;

const SendFeedBackStep4 = ({ onChangeStep, onClose }: SendFeedBackStep1Props) => {
  const { styles } = useStyles(stylesheet);
  const { mutateAsync: sendUserFeedback, isPending: isSendingFeedback } = useUserFeedbackMutation();

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FeedbackSchemaData>({
    defaultValues: {
      feedback: '',
    },
    resolver: zodResolver(feedbackSchema),
  });

  const onSubmit = async (data: FeedbackSchemaData) => {
    try {
      await sendUserFeedback(data.feedback);
      onChangeStep?.(STEPS.THANKS);
      reset();
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <View style={styles.content}>
      <ThemedText style={styles.title}>Give Your Feedback</ThemedText>

      <Spacer height={32} />

      <ThemedText type='defaultMedium'>Please tell us how we can improve the app</ThemedText>

      <Spacer height={16} />

      <Controller
        control={control}
        name='feedback'
        render={({ field: { onChange, value } }) => (
          <TextareaInput
            placeholder='Please tell us how we can improve the app'
            value={value}
            onChangeText={onChange}
            error={errors.feedback?.message}
            editable={!isSendingFeedback}
            inputContainer={styles.inputContainer}
          />
        )}
      />

      <Spacer height={32} />

      <View style={styles.buttonContainer}>
        <CustomButton
          textType='defaultBold'
          type='primary'
          style={styles.confirmButton}
          onPress={handleSubmit(onSubmit)}
          isLoading={isSendingFeedback}
        >
          Give feedback
        </CustomButton>

        <TouchableOpacity activeOpacity={0.7} onPress={onClose}>
          <ThemedText style={styles.textPrimary} type='defaultBold'>
            I'll do it later
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const SendFeedBackStep5 = ({ onClose }: SendFeedBackStep1Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.content}>
      <Image source={require('@/assets/images/logo_ok.png')} style={styles.image} />

      <Spacer height={24} />

      <ThemedText style={styles.title}>Thank You For Your Feedback</ThemedText>

      <Spacer height={16} />

      <ThemedText style={styles.description} type='small'>
        Your feedback really helps us improve!
      </ThemedText>

      <Spacer height={28} />

      <View style={styles.buttonContainer}>
        <TouchableOpacity activeOpacity={0.7} onPress={onClose}>
          <ThemedText style={styles.textPrimary} type='defaultBold'>
            Got it!
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  inputContainer: {
    backgroundColor: theme.colors.background,
    minHeight: 154,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  description: {
    color: theme.colors.whiteOpacity56,
    textAlign: 'center',
    maxWidth: 300,
  },
  confirmButton: {
    width: '100%',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 16,
    width: '100%',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    ...theme.fw700,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    lineHeight: 28,
    letterSpacing: 0,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 999,
    overflow: 'hidden',
  },
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 24,
  },
  container: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 32,
    paddingVertical: 48,
    paddingHorizontal: { [mq.only.width(430)]: 24, [mq.only.width('xs')]: 20 },
    width: '100%',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
}));
