import { Icons } from '@/assets/icons';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import TextInput, { CustomTextInputProps } from './ui/TextInput';

type Props = {} & CustomTextInputProps;

export const SearchInput = ({ inputStyle, ...props }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <TextInput
        inputStyle={[inputStyle, styles.input]}
        leftIcon={<Icons.Search size={20} color='#FFFFFF' />}
        {...props}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    // flex: 1,
  },
  input: {
    borderRadius: 999,
    // flex: 1,
  },
}));
