import { ConfirmationModal } from './ui/ConfirmationModal';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useUserStore } from '@/store/user';
import { Icons } from '@/assets/icons';
import { router } from 'expo-router';

type Props = {
  isVisible: boolean;
};

export const SetupAddressPatronModal = ({ isVisible }: Props) => {
  const { styles } = useStyles(stylesheet);
  const setIsShowSetupAddressPatronModal = useUserStore.use.setIsShowSetupAddressPatronModal();

  const handleClose = () => {
    setIsShowSetupAddressPatronModal(false);
  };

  const handleSetupAddressConfirm = () => {
    handleClose();
    router.push({
      pathname: '/(app)/set-address',
    });
  };

  return (
    <ConfirmationModal
      icon={<Icons.Verify size={40} color='#D9FF03' />}
      isVisible={isVisible}
      onClose={handleClose}
      onCancel={handleClose}
      onConfirm={handleSetupAddressConfirm}
      confirmText='Setup Your Address'
      cancelText='Next Time'
      title='Congrats, you have become Founding Patron!'
      description={'Setup your shipping address to receive your exclusive Rabid Coin.'}
      descriptionStyle={styles.descriptionStyle}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  descriptionStyle: {
    fontSize: 16,
    ...theme.fw400,
  },
}));
