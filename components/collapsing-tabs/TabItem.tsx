import { memo, PropsWithChildren, RefObject } from 'react';
import { TabContextProvider } from './tab.context';

export type RefTabItem = {
  scrollToTop: () => void;
};

type Props = {
  tabNames: string[];
  tabName: string;
  label?: string;
  refTabItem: RefObject<RefTabItem>;
};

export type TabsWithProps = PropsWithChildren<Props>;

export const TabItem = memo(({ tabName, tabNames = [], label, refTabItem, children }: TabsWithProps) => {
  return <TabContextProvider value={{ tabName, tabNames, label, refTabItem }}>{children}</TabContextProvider>;
});
