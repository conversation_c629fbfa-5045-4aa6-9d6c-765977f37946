import { useCollapsingTabsContext } from '@/contexts/app.context';
import { useAnimateList } from './useAnimateList';
import { FlashList, FlashListProps } from '@shopify/flash-list';
import { useCallback, useImperativeHandle, useRef } from 'react';
import { ViewabilityConfig } from 'react-native';
import { FlashListAnimate } from '../FlashListAnimate';
// import 'react-native-get-random-values';
// import { v4 as uuid } from 'uuid';
import { useTabContext } from './tab.context';
import { AnimatedRef, scrollTo, SharedValue } from 'react-native-reanimated';

type Props<T> = {
  listKey?: string;
} & FlashListProps<T>;

export const TabFlashList = <T,>(props: Props<T>) => {
  // const [listKey, setListKey] = useState(uuid());
  const { sharedProps, pullPosition } = useCollapsingTabsContext();
  const { refTabItem } = useTabContext();
  const { ref, scrollHandler } = useAnimateList();
  const { contentContainerStyle, scrollIndicatorInsets, scrollEventThrottle } = sharedProps;

  const viewabilityConfig = useRef<ViewabilityConfig>({
    waitForInteraction: false,
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 1000,
  }).current;

  const scrollToOffset = useCallback(
    (offset: number) => {
      const flashListRef = ref as AnimatedRef<FlashList<T>>;
      if (flashListRef.current) {
        flashListRef.current.scrollToOffset({ offset, animated: true });
      }
      scrollTo(ref, 0, 0, false);
    },
    [ref]
  );

  useImperativeHandle(refTabItem, () => ({
    scrollToTop: () => {
      scrollToOffset(0);
    },
  }));

  const handlePullPositionChange = useCallback(
    (position: SharedValue<number>) => {
      'worklet';
      pullPosition.value = position.value;
    },
    [pullPosition]
  );

  return (
    <FlashListAnimate
      {...props}
      flashListKey={props?.listKey}
      ref={ref as AnimatedRef<FlashList<T>>}
      onScroll={scrollHandler}
      {...(sharedProps as any)}
      contentContainerStyle={{
        ...(contentContainerStyle && typeof contentContainerStyle === 'object' ? contentContainerStyle : {}),
        ...props.contentContainerStyle,
      }}
      scrollIndicatorInsets={{ ...props.scrollIndicatorInsets, ...scrollIndicatorInsets }}
      scrollEventThrottle={scrollEventThrottle || props.scrollEventThrottle || 16}
      viewabilityConfig={viewabilityConfig}
      onPullPositionChange={handlePullPositionChange}
    />
  );
};
