import React, { PropsWithChildren, RefObject, useCallback, useMemo, useRef } from 'react';
import { useCollapsingTabsContext } from '@/contexts/app.context';
import { View, ViewStyle } from 'react-native';
import Animated, { useEvent, useHandler } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import PagerView, { PagerViewOnPageScrollEvent } from 'react-native-pager-view';
import { RefTabItem, TabItem, TabsWithProps } from './TabItem';
import { TabOptionValue, useTabProps } from './hooks/useTabProps';
import { TabHeader } from './TabHeader';
import { TabBar } from './TabBar';
import { TabWrapper } from './TabWrapper';

const AnimatedPagerView = Animated.createAnimatedComponent(PagerView);

export type TabBarProps = {
  tabOptions: Map<string, TabOptionValue>;
  tabNamesArray: string[];
  onTabPress: (index: number) => void;
};

type Props = {
  renderHeader?: React.ReactNode | null | (() => React.JSX.Element | null);
  headerContainerStyle?: ViewStyle;
  tabBarStyle?: ViewStyle;
  children: React.ReactElement<TabsWithProps> | React.ReactElement<TabsWithProps>[];
  renderCustomTabBar?: (props: TabBarProps) => React.ReactNode;
};

function usePageScrollHandler(
  handlers: {
    onPageScroll?: (event: { offset: number; position: number }, context?: any) => void;
  },
  dependencies?: any[]
) {
  const { context, doDependenciesDiffer } = useHandler(handlers, dependencies);
  const subscribeForEvents = ['onPageScroll'];

  return useEvent(
    (event: any) => {
      'worklet';
      const { onPageScroll } = handlers;
      if (onPageScroll && event.eventName.endsWith('onPageScroll')) {
        onPageScroll(event, context);
      }
    },
    subscribeForEvents,
    doDependenciesDiffer
  );
}

export const TabNavigator = ({
  children,
  renderHeader,
  headerContainerStyle: headerContainerStyleOvr,
  renderCustomTabBar,
}: PropsWithChildren<Props>) => {
  const { styles } = useStyles(stylesheet);
  const [tabOptions, tabNamesArray] = useTabProps(children, TabWrapper);

  const refParent = useRef<RefObject<RefTabItem>[]>([]);

  const { tabBarStyle, progressIndicator, onIndexChange, handleTabBarLayout, tabIndex, refPager } =
    useCollapsingTabsContext();

  const handlePageScroll = useCallback(
    (event: PagerViewOnPageScrollEvent) => {
      const { offset, position } = event.nativeEvent;
      progressIndicator.value = position + offset;
    },
    [progressIndicator]
  );

  const pageScrollHandler = usePageScrollHandler(
    {
      onPageScroll: (e: { offset: number; position: number }) => {
        'worklet';
        progressIndicator.value = e.position + e.offset;
      },
    },
    [progressIndicator]
  );

  const handlePageSelected = useCallback(
    (event: any) => {
      onIndexChange(event.nativeEvent.position);
    },
    [onIndexChange]
  );

  const handleTabPress = (index: number) => {
    onIndexChange(index);

    if (tabIndex === index) {
      const currentRef = refParent.current?.[index];
      currentRef?.current?.scrollToTop();
    }
  };

  if (refParent.current.length !== tabNamesArray.length) {
    refParent.current = Array(tabNamesArray.length)
      .fill(1)
      .map((_, i) => refParent.current[i] || React.createRef());
  }

  return (
    <>
      <TabHeader renderHeader={renderHeader} headerContainerStyle={headerContainerStyleOvr} />

      <Animated.View style={[styles.tabsContainer, tabBarStyle]} onLayout={handleTabBarLayout}>
        {renderCustomTabBar ? (
          renderCustomTabBar({ tabOptions, tabNamesArray, onTabPress: handleTabPress })
        ) : (
          <TabBar tabOptions={tabOptions} tabNamesArray={tabNamesArray} onTabPress={handleTabPress} />
        )}
      </Animated.View>

      <AnimatedPagerView
        initialPage={tabIndex}
        ref={refPager}
        style={styles.fullFlex}
        onPageSelected={handlePageSelected}
        onPageScroll={pageScrollHandler}
        offscreenPageLimit={tabNamesArray.length}
      >
        {useMemo(
          () =>
            tabNamesArray.map((tabName, index) => {
              const tabOption = tabOptions.get(tabName)!;
              const { child, ...props } = tabOption;
              const refTabItem = refParent.current?.[index];

              return (
                <View
                  key={tabName}
                  collapsable={false}
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                >
                  <TabItem {...props} tabNames={tabNamesArray} tabName={tabName} refTabItem={refTabItem}>
                    {child}
                  </TabItem>
                </View>
              );
            }),
          [tabNamesArray, tabOptions]
        )}
      </AnimatedPagerView>
    </>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  tabBarStyle: {
    backgroundColor: theme.colors.neutralBackground,
    // width: 'auto',
  },
  tabBarContentContainerStyle: {
    // width: 'auto',
  },
  tabBarIndicatorContainerStyle: {},
  tabBarIndicatorStyle: {
    backgroundColor: theme.colors.primary,
  },
  tabBarLabelStyle: {
    ...theme.fw500,
    fontSize: 16,
  },
  tabsContainer: {
    backgroundColor: theme.colors.neutralBackground,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    zIndex: 3,
  },
  tabBarItemStyle: {
    width: 'auto',
  },
  fullFlex: {
    flex: 1,
  },
}));
