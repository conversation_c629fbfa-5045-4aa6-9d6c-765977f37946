import { router } from 'expo-router';
import { PropsWithChildren } from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';

type Props = {
  episodeId?: string;
} & TouchableOpacityProps;

export const TouchEpisodeDetail = ({ children, episodeId, ...props }: PropsWithChildren<Props>) => {
  const handlePress = () => {
    if (!episodeId) return;

    router.push({
      pathname: '/(app)/episode/[episodeId]',
      params: {
        episodeId,
      },
    });
  };

  return (
    <TouchableOpacity disabled={!episodeId} activeOpacity={0.7} onPress={handlePress} {...props}>
      {children}
    </TouchableOpacity>
  );
};
