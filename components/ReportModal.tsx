import { ConfirmationModal } from './ui/ConfirmationModal';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Pressable, View } from 'react-native';
import { Spacer } from './Spacer';
import { ThemedText } from './ThemedText';
import { useState } from 'react';

type Props = {
  isVisible: boolean;
  isLoading?: boolean;
  onClose: () => void;
  onConfirm: (reasons: string[]) => void;
};

const reportReasons = ['Spam', 'Inappropriate', 'Harassment', 'Fake', 'Other'];

export const ReportModal = ({ isVisible, isLoading, onClose, onConfirm }: Props) => {
  const { styles } = useStyles(stylesheet);
  const [selectedReasons, setSelectedReasons] = useState<string[]>([]);

  const handleSelectReason = (reason: string) => {
    setSelectedReasons((prev) => (prev.includes(reason) ? prev.filter((r) => r !== reason) : [...prev, reason]));
  };

  const handleConfirm = () => {
    onConfirm(selectedReasons);
  };

  const handleClose = () => {
    setSelectedReasons([]);
    onClose();
  };

  return (
    <ConfirmationModal
      isVisible={isVisible}
      onClose={handleClose}
      onCancel={handleClose}
      onConfirm={handleConfirm}
      title='Report This Post'
      description={
        <View style={styles.descriptionBox}>
          <Spacer height={16} />

          <ThemedText type='defaultMedium' style={styles.confirmDescriptionText}>
            Why you report this Post?
          </ThemedText>

          <Spacer height={32} />

          <View style={styles.reportReasonsBox}>
            {reportReasons.map((reason) => {
              const isSelected = selectedReasons.includes(reason);

              return (
                <Pressable
                  disabled={isLoading}
                  style={[styles.reasonTouch, isSelected && styles.reasonSelectedTouch]}
                  key={reason}
                  onPress={() => handleSelectReason(reason)}
                >
                  <ThemedText key={reason} type='defaultMedium' style={[isSelected && styles.reasonTextActive]}>
                    {reason}
                  </ThemedText>
                </Pressable>
              );
            })}
          </View>
        </View>
      }
      cancelText='Cancel'
      confirmText='Confirm'
      isLoading={isLoading}
      disabledConfirm={selectedReasons.length === 0}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  reasonSelectedTouch: {
    backgroundColor: theme.colors.primary,
    borderColor: '#0E100F4D',
  },
  reasonTouch: {
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 999,
  },
  reasonTextActive: {
    color: theme.colors.neutralBackground,
  },
  reportReasonsBox: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  descriptionBox: {
    width: '100%',
  },
  confirmDescriptionText: {
    color: theme.colors.whiteOpacity64,
  },
}));
