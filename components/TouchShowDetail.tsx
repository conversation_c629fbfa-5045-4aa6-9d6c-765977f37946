import { router } from 'expo-router';
import { PropsWithChildren } from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';

type Props = {
  podcastId?: string;
} & TouchableOpacityProps;

export const TouchShowDetail = ({ children, podcastId, ...props }: PropsWithChildren<Props>) => {
  const handlePress = () => {
    if (!podcastId) return;

    router.push({
      pathname: '/(app)/podcast/[podcastId]',
      params: {
        podcastId,
      },
    });
  };

  return (
    <TouchableOpacity disabled={!podcastId} activeOpacity={0.7} onPress={handlePress} {...props}>
      {children}
    </TouchableOpacity>
  );
};
