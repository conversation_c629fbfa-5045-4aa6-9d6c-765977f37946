import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { Spacer } from '../Spacer';
import { ThemedText } from '../ThemedText';
import { Avatar } from './Avatar';
import { Button } from './Button';
import { type IFollow } from '@/apis/follow';
import { Icons } from '@/assets/icons';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

export type User = {
  id: string;
  username: string;
  avatar?: string | null;
  isVerified?: boolean;
  isFollowing?: boolean;
};

type Props = {
  user: IFollow;
  onFollowToggle?: (userId: string, isFollowed: boolean) => void;
  onUserPress?: (user: IFollow) => void;
  showFollowButton?: boolean;
};

export const UserListItem = ({ user, onFollowToggle, onUserPress, showFollowButton = true }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const handleFollowPress = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    onFollowToggle?.(user.id.toString(), user.isFollowed);
  };

  const handleUserPress = () => {
    onUserPress?.(user);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.userInfo} onPress={handleUserPress} disabled={!onUserPress}>
        <Avatar image={user.avatar || undefined} size={48} />
        <Spacer width={20} />
        <View style={styles.userDetails}>
          <View style={styles.usernameContainer}>
            <ThemedText style={styles.username} type='defaultSemiBold' numberOfLines={1}>
              {user.username}
            </ThemedText>
            {user.type === 'premium' && (
              <View style={styles.verifiedBadge}>
                <Icons.Verify size={18} />
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>

      {showFollowButton && (
        <Button
          type={!user.isFollowed ? 'default' : 'outline'}
          style={!user.isFollowed ? styles.followButton : styles.followingButton}
          onPress={handleFollowPress}
        >
          <ThemedText
            style={[
              styles.followButtonText,
              !user.isFollowed ? styles.followButtonTextDefault : styles.followingButtonText,
            ]}
          >
            {!user.isFollowed ? 'Follow' : 'Following'}
          </ThemedText>
        </Button>
      )}
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userDetails: {
    flex: 1,
  },
  usernameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 34,
  },
  username: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    ...theme.fw500,
    lineHeight: 24,
  },
  verifiedBadge: {
    borderRadius: 999,
    width: 23,
    height: 23,
    paddingTop: 3,
    paddingLeft: 2.5,
    paddingRight: 2.5,
    paddingBottom: 1.5,
    marginLeft: 5.5,
  },
  verifiedImage: {
    width: '100%',
    height: '100%',
  },
  followButton: {
    paddingHorizontal: 14,
    paddingVertical: 7,
    width: 84,
    height: 38,
  },
  followButtonText: {
    fontSize: 12,
    ...theme.fw600,
    lineHeight: 24,
    letterSpacing: 0,
  },
  followButtonTextDefault: {
    color: theme.colors.neutralBackground,
  },
  followingButton: {
    backgroundColor: '#D9FF031A',
    paddingHorizontal: 14,
    paddingVertical: 7,
    minWidth: 84,
    height: 38,
  },
  followingButtonText: {
    color: theme.colors.primary,
  },
}));
