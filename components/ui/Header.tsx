import { Icons } from '@/assets/icons';
import { router } from 'expo-router';
import { ReactNode } from 'react';
import { Image, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from '../ThemedText';
import Animated, { AnimatedProps } from 'react-native-reanimated';

type Props = {
  isBack?: boolean;
  isClose?: boolean;
  closeStyle?: ViewStyle;
  leftAction?: ReactNode;
  rightAction?: ReactNode;
  title?: string;
  titleStyle?: TextStyle;
  leftStyle?: ViewStyle;
  rightStyle?: ViewStyle;
  hideHeader?: boolean;
  titleAnimationProps?: AnimatedProps<ViewStyle>;
  onBackPress?: () => void;
};

export const Header = ({
  isBack = false,
  isClose = false,
  closeStyle,
  leftAction,
  title,
  titleStyle,
  rightAction,
  leftStyle,
  rightStyle,
  hideHeader,
  onBackPress,
  titleAnimationProps,
}: Props) => {
  const { styles } = useStyles(stylesheet);

  const handleGoBack = () => {
    if (onBackPress) return onBackPress();

    if (router.canGoBack()) {
      router.back();
    }
  };

  const handleClose = () => {
    if (router.canDismiss()) {
      router.dismiss();
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.left, leftStyle]}>
        {isBack ? (
          <TouchableOpacity activeOpacity={0.7} onPress={handleGoBack}>
            <Icons.BackIcon size={24} />
          </TouchableOpacity>
        ) : (
          leftAction
        )}
      </View>

      <Animated.View style={[styles.center, titleAnimationProps]}>
        {title != null || hideHeader ? (
          <ThemedText style={[titleStyle]} type='subtitleSemiBold' numberOfLines={1} ellipsizeMode='middle'>
            {title}
          </ThemedText>
        ) : (
          <Image source={require('@/assets/images/logo.png')} resizeMode='contain' style={styles.logo} />
        )}
      </Animated.View>

      <View style={[styles.right, rightStyle]}>
        {isClose ? (
          <TouchableOpacity onPress={handleClose} style={closeStyle}>
            <Icons.Close size={24} />
          </TouchableOpacity>
        ) : (
          rightAction
        )}
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    height: 50,
    paddingVertical: 5,
  },
  center: {},
  left: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flex: 1,
    alignSelf: 'center',
  },
  right: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flex: 1,
  },
  logo: {
    height: '100%',
  },
}));
